# PHP聊天列表分离方案

本文档讨论如何将聊天列表功能从FastAPI应用中分离出来，使用PHP来接管，并分离前端页面。

## 系统分离方案讨论

### 1. 整体架构设计

将系统分为三个部分：
- FastAPI后端：保留WebSocket实时通信和核心聊天功能
- PHP后端：负责聊天列表管理和用户界面
- 分离的前端：可以是纯HTML/JS或使用前端框架

### 2. 数据共享方式

#### 数据库共享
- PHP和FastAPI共享同一个MySQL数据库
- 聊天记录、用户信息、聊天室信息等核心数据保持在同一数据库中
- 需要设计清晰的数据库访问权限和表结构

```
[FastAPI应用] ←→ [共享数据库] ←→ [PHP应用]
```

#### API接口方式
- FastAPI提供REST API接口给PHP调用
- PHP通过API获取聊天列表、历史消息等数据
- 优点是解耦更彻底，缺点是可能增加延迟

```
[PHP应用] ←→ [FastAPI API接口] ←→ [FastAPI核心服务]
```

### 3. PHP接管聊天列表的实现方案

#### 方案A：PHP直接读取数据库
```php
<?php
// 连接到共享的MySQL数据库
$conn = new mysqli("localhost", "username", "password", "ws_chat");

// 获取用户的聊天列表
function getUserChatrooms($userId) {
    global $conn;
    $sql = "SELECT c.* FROM chatrooms c 
            JOIN user_chatroom uc ON c.id = uc.chatroom_id 
            WHERE uc.user_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $chatrooms = [];
    while($row = $result->fetch_assoc()) {
        // 获取最后一条消息
        $lastMessageSql = "SELECT * FROM messages 
                          WHERE chatroom_id = ? 
                          ORDER BY created_at DESC LIMIT 1";
        $msgStmt = $conn->prepare($lastMessageSql);
        $msgStmt->bind_param("i", $row['id']);
        $msgStmt->execute();
        $lastMessage = $msgStmt->get_result()->fetch_assoc();
        
        $row['last_message'] = $lastMessage;
        $chatrooms[] = $row;
    }
    
    return $chatrooms;
}
?>
```

#### 方案B：PHP通过API调用FastAPI
```php
<?php
// 通过API获取聊天列表
function getUserChatrooms($userId) {
    $apiUrl = "http://fastapi-server/api/chat/list?user_id=" . urlencode($userId);
    $options = [
        'http' => [
            'header' => "Content-Type: application/json\r\n",
            'method' => 'GET'
        ]
    ];
    $context = stream_context_create($options);
    $response = file_get_contents($apiUrl, false, $context);
    
    return json_decode($response, true);
}
?>
```

### 4. 前端分离方案

#### 方案A：PHP渲染页面，JavaScript处理WebSocket
```php
<!-- PHP生成的聊天列表页面 -->
<div class="chat-list">
    <?php foreach($chatrooms as $room): ?>
    <div class="chat-item" data-id="<?= $room['id'] ?>">
        <div class="chat-name"><?= htmlspecialchars($room['name']) ?></div>
        <div class="last-message"><?= htmlspecialchars($room['last_message']['content']) ?></div>
    </div>
    <?php endforeach; ?>
</div>

<script>
// 连接到FastAPI的WebSocket
const socket = new WebSocket(`ws://fastapi-server/api/chat/connect_chat?user_code=${userId}`);

// 处理新消息
socket.onmessage = (event) => {
    const data = JSON.parse(event.data);
    // 更新UI
    updateChatUI(data);
};
</script>
```

#### 方案B：完全分离的前端（SPA）
- 使用Vue.js、React或Angular构建前端SPA
- PHP提供API接口获取聊天列表
- FastAPI提供WebSocket连接和消息API
- 前端通过API与两个后端交互

### 5. 认证和会话管理

这是一个关键问题，需要在PHP和FastAPI之间共享用户认证信息：

#### 方案A：共享会话（Session）
- 使用Redis存储会话数据
- PHP和FastAPI都可以访问相同的会话数据
- 用户只需登录一次

#### 方案B：令牌认证（Token）
- PHP登录后生成JWT令牌
- 前端存储令牌并在连接WebSocket时提供
- FastAPI验证令牌的有效性

```php
<?php
// PHP生成JWT令牌
function generateToken($userId) {
    $key = "your_secret_key";
    $payload = [
        "user_id" => $userId,
        "exp" => time() + 3600 // 1小时过期
    ];
    
    // 简化的JWT生成
    $header = base64_encode(json_encode(["alg" => "HS256", "typ" => "JWT"]));
    $payload = base64_encode(json_encode($payload));
    $signature = hash_hmac('sha256', "$header.$payload", $key);
    
    return "$header.$payload.$signature";
}
?>

<script>
// 前端使用令牌连接WebSocket
const token = "<?= generateToken($userId) ?>";
const socket = new WebSocket(`ws://fastapi-server/api/chat/connect_chat?token=${token}`);
</script>
```

### 6. 需要修改的FastAPI代码

为了支持PHP接管，需要在FastAPI中添加以下API：

```python
@app.get("/api/chat/list")
async def get_chat_list(user_id: str, db: Session = Depends(get_db)):
    """获取用户的聊天列表"""
    user = db.execute(select(User).filter(User.id == user_id)).scalar_one_or_none()
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 获取用户的聊天室
    chatrooms = []
    for chatroom in user.chatrooms:
        chatroom_dict = chatroom.to_dict()
        chatrooms.append(chatroom_dict)
    
    return {"code": 200, "msg": "成功", "data": chatrooms}

# 添加令牌验证的WebSocket连接
@app.websocket("/api/chat/connect_chat_with_token")
async def connect_chat_with_token(websocket: WebSocket, token: str, db: Session = Depends(get_db)):
    # 验证令牌
    try:
        user_id = verify_token(token)  # 实现令牌验证函数
        await cm.connect(websocket, user_id, db)
    except WebSocketDisconnect:
        await cm.disconnect(user_id)
    except Exception as e:
        print(f"WebSocket连接异常: {str(e)}")
        await cm.disconnect(user_id)
```

### 7. 部署考虑

#### 方案A：同一服务器
- PHP和FastAPI部署在同一服务器
- 使用Nginx作为反向代理，根据路径分发请求
- WebSocket请求转发到FastAPI，其他请求转发到PHP

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # PHP处理
    location / {
        root /var/www/html;
        index index.php;
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }
    
    # FastAPI处理
    location /api/chat/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

#### 方案B：分离服务器
- PHP和FastAPI部署在不同服务器
- 需要处理跨域问题
- 可以使用API网关统一管理请求

### 8. 潜在挑战和注意事项

1. **数据一致性**：两个系统访问同一数据库可能导致数据不一致
2. **性能开销**：API调用方式可能增加延迟
3. **认证同步**：确保用户在两个系统中的认证状态同步
4. **跨域问题**：如果前端需要同时访问两个后端，需要处理CORS
5. **WebSocket连接**：确保从PHP页面正确连接到FastAPI的WebSocket
6. **错误处理**：两个系统之间的错误传递和处理
7. **部署复杂性**：维护两个不同技术栈的系统增加了部署复杂性

## 总结

将聊天列表功能从FastAPI分离到PHP是可行的，主要有两种方案：
1. 共享数据库方案：简单直接，但耦合度高
2. API接口方案：解耦更彻底，但可能增加延迟

建议采用渐进式迁移：
1. 先在FastAPI中添加必要的API接口
2. 开发PHP页面，通过API获取数据
3. 实现认证共享机制
4. 逐步将用户界面迁移到PHP
5. 保留FastAPI作为WebSocket和核心聊天功能的后端

这种方式可以平滑过渡，同时降低风险。
