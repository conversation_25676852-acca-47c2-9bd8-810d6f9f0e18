# FastAPI与PHP分离实施方案

## 1. 系统架构设计

### 1.1 分离后的架构

```
+----------------+      +----------------+
|                |      |                |
|  PHP 应用      |<---->|  FastAPI 应用  |
|  (页面和API)   |      |  (WebSocket)   |
|                |      |                |
+-------+--------+      +--------+-------+
        |                        |
        v                        v
+-------+------------------------+-------+
|                                        |
|             共享数据库                  |
|          (MySQL + Redis)               |
|                                        |
+----------------------------------------+
```

### 1.2 职责划分

**FastAPI 负责**:
- WebSocket连接管理
- 实时消息处理和分发
- 心跳检测机制
- Redis发布订阅

**PHP 负责**:
- 用户认证和会话管理
- 聊天列表和聊天室管理
- 消息历史记录查询
- 文件上传处理
- 卡片消息管理
- 前端页面渲染

## 2. 修改FastAPI代码

### 2.1 保留的核心文件

- `app/chat_manager/server.py` - WebSocket连接管理
- `app/models.py` - 数据库模型
- `app/database.py` - 数据库连接
- `utils/redis_util.py` - Redis工具
- `main.py` - 主应用入口(需修改)

### 2.2 需要修改的FastAPI代码

#### 2.2.1 修改WebSocket连接认证

在`app/chat_manager/chat.py`中添加基于令牌的WebSocket连接：

```python
@app.websocket("/connect_chat_with_token")
async def connect_chat_with_token(websocket: WebSocket, token: str, db: Session = Depends(get_db)):
    """使用令牌连接WebSocket"""
    try:
        # 验证令牌
        user_id = verify_token(token)
        if not user_id:
            await websocket.close(code=1008)  # 策略违规
            return

        # 连接WebSocket
        await cm.connect(websocket, user_id, db)
    except WebSocketDisconnect:
        await cm.disconnect(user_id)
    except Exception as e:
        print(f"WebSocket连接异常: {str(e)}")
        try:
            await cm.disconnect(user_id)
        except:
            pass
```

#### 2.2.2 添加令牌验证函数

在`app/utils/`目录下创建`token_util.py`：

```python
import jwt
import time
from config.get_config import config

# 从配置中获取密钥
SECRET_KEY = config.get('jwt', {}).get('secret_key', 'your_secret_key')
ALGORITHM = "HS256"

def verify_token(token: str) -> str:
    """验证JWT令牌并返回用户ID"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id = payload.get("user_id")
        exp = payload.get("exp")

        # 检查令牌是否过期
        if exp and time.time() > exp:
            return None

        return user_id
    except:
        return None
```

#### 2.2.3 简化main.py

修改`main.py`，移除不需要的路由：

```python
import uvicorn
from fastapi import FastAPI, Depends
import asyncio
from redis import asyncio as aioredis
import json
import os
from app.chat_manager import chat
from config.get_config import config
from app.database import engine, get_db
from app.models import Base
from sqlalchemy.orm import Session

# redis配置
url = (config['db_redis']['host'], config['db_redis']['port'])
host = config['db_redis']['host']
db = config['db_redis']['db']
timeout = config['db_redis']['timeout']
password = config['db_redis']['password']
port = config['db_redis']['port']

# 初始化数据库
Base.metadata.create_all(bind=engine)

# 初始化app
app = FastAPI(title="Ws Chat", description="WebSocket聊天服务", version="1.0.0")

# 只保留WebSocket相关路由
app.include_router(chat.app, prefix='/api/chat', tags=['Chat'])

# 添加CORS支持
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境应该限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 保存后台任务的引用，以便在关闭时取消
background_tasks = []

@app.on_event('startup')
async def on_startup():
    print(f"订阅初始化:{os.getpid()}")

    # 启动WebSocket心跳检查任务
    try:
        print("启动WebSocket心跳检查任务...")
        await chat.cm.start_heartbeat_check()
        print("WebSocket心跳检查任务已启动")
    except Exception as e:
        print(f"启动WebSocket心跳检查任务时出错: {e}")

    # 执行消息订阅机制
    loop = asyncio.get_event_loop()
    task = loop.create_task(register_pubsub())
    # 保存任务引用
    background_tasks.append(task)

@app.on_event('shutdown')
async def on_shutdown():
    # 停止心跳检查任务
    try:
        await chat.cm.stop_heartbeat_check()
    except Exception as e:
        print(f"停止WebSocket心跳检查任务时出错: {e}")

    # 断开所有WebSocket连接
    try:
        for user_id in list(chat.cm.websocket_connections.keys()):
            try:
                await chat.cm.disconnect(user_id)
            except Exception as e:
                print(f"断开用户 {user_id} 连接时出错: {e}")
    except Exception as e:
        print(f"断开WebSocket连接时出错: {e}")

    # 取消所有后台任务
    for task in background_tasks:
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass

# 保留Redis发布订阅功能
async def register_pubsub():
    """注册Redis PubSub并处理消息"""
    # 创建Redis连接池
    pool = None
    psub = None

    try:
        # 创建Redis连接
        pool = aioredis.from_url(
            "redis://{}".format(host), db=db, password=password, port=port,
            encoding="utf-8", decode_responses=True,
            retry_on_timeout=True,
            socket_keepalive=True,
            health_check_interval=30
        )

        # 创建发布订阅对象
        psub = pool.pubsub()

        # 订阅聊天频道
        await psub.subscribe("chat")

        # 处理消息
        async for message in psub.listen():
            if message["type"] == "message":
                try:
                    # 解析消息
                    data = json.loads(message["data"])
                    # 处理消息
                    await chat.cm.handle_redis_message(data)
                except Exception as e:
                    print(f"处理Redis消息时出错: {e}")
    except Exception as e:
        print(f"Redis订阅出错: {e}")
    finally:
        # 清理资源
        if psub:
            await psub.unsubscribe("chat")
            await psub.close()
        if pool:
            await pool.close()

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=298)
```

## 3. PHP实现

### 3.1 PHP目录结构

```
php_chat/
├── config/
│   └── config.php         # 配置文件
├── includes/
│   ├── db.php             # 数据库连接
│   ├── auth.php           # 认证相关
│   ├── token.php          # JWT令牌生成
│   └── chat.php           # 聊天相关函数
├── api/
│   ├── login.php          # 登录API
│   ├── chatrooms.php      # 聊天室API
│   ├── messages.php       # 消息API
│   ├── upload.php         # 上传API
│   └── cards.php          # 卡片消息API
├── assets/
│   ├── css/               # 样式文件
│   ├── js/                # JavaScript文件
│   └── img/               # 图片资源
├── templates/
│   ├── header.php         # 页面头部
│   ├── footer.php         # 页面底部
│   └── chat.php           # 聊天页面模板
├── index.php              # 主页
└── chat.php               # 聊天页面
```

### 3.2 PHP核心功能实现

#### 3.2.1 数据库连接

`includes/db.php`:
```php
<?php
class Database {
    private $host = "localhost";
    private $user = "root";
    private $password = "root";
    private $database = "test";
    private $conn;

    public function __construct() {
        try {
            $this->conn = new PDO(
                "mysql:host=$this->host;dbname=$this->database",
                $this->user,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $e) {
            die("连接失败: " . $e->getMessage());
        }
    }

    public function getConnection() {
        return $this->conn;
    }
}
?>
```

#### 3.2.2 JWT令牌生成

`includes/token.php`:
```php
<?php
class TokenManager {
    private $secret_key = "your_secret_key";

    public function generateToken($user_id) {
        $payload = [
            "user_id" => $user_id,
            "exp" => time() + 3600 * 24 // 24小时过期
        ];

        $header = base64_encode(json_encode([
            "alg" => "HS256",
            "typ" => "JWT"
        ]));

        $payload_encoded = base64_encode(json_encode($payload));
        $signature = hash_hmac('sha256', "$header.$payload_encoded", $this->secret_key);

        return "$header.$payload_encoded.$signature";
    }
}
?>

#### 3.2.3 用户认证

`includes/auth.php`:
```php
<?php
require_once 'db.php';
require_once 'token.php';

class Auth {
    private $db;
    private $tokenManager;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->tokenManager = new TokenManager();
    }

    public function login($username) {
        try {
            // 查找用户
            $stmt = $this->db->prepare("SELECT * FROM users WHERE username = :username");
            $stmt->bindParam(':username', $username);
            $stmt->execute();

            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            // 如果用户不存在，创建新用户
            if (!$user) {
                $userId = $this->createUser($username);
            } else {
                $userId = $user['id'];
            }

            // 生成令牌
            $token = $this->tokenManager->generateToken($userId);

            return [
                'user_id' => $userId,
                'username' => $username,
                'token' => $token
            ];
        } catch (PDOException $e) {
            return false;
        }
    }

    private function createUser($username) {
        // 生成唯一ID
        $userId = uniqid();

        $stmt = $this->db->prepare("INSERT INTO users (id, username) VALUES (:id, :username)");
        $stmt->bindParam(':id', $userId);
        $stmt->bindParam(':username', $username);
        $stmt->execute();

        return $userId;
    }
}
?>
```

#### 3.2.4 聊天室管理

`includes/chat.php`:
```php
<?php
require_once 'db.php';

class ChatManager {
    private $db;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    public function getUserChatrooms($userId) {
        try {
            $stmt = $this->db->prepare("
                SELECT c.* FROM chatrooms c
                JOIN user_chatroom uc ON c.id = uc.chatroom_id
                WHERE uc.user_id = :user_id
            ");
            $stmt->bindParam(':user_id', $userId);
            $stmt->execute();

            $chatrooms = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                // 获取聊天室成员
                $memberStmt = $this->db->prepare("
                    SELECT u.id, u.username, u.avatar
                    FROM users u
                    JOIN user_chatroom uc ON u.id = uc.user_id
                    WHERE uc.chatroom_id = :chatroom_id
                ");
                $memberStmt->bindParam(':chatroom_id', $row['id']);
                $memberStmt->execute();
                $members = $memberStmt->fetchAll(PDO::FETCH_ASSOC);

                // 获取最后一条消息
                $msgStmt = $this->db->prepare("
                    SELECT m.*, u.username as sender_name
                    FROM messages m
                    LEFT JOIN users u ON m.sender_id = u.id
                    WHERE m.chatroom_id = :chatroom_id
                    ORDER BY m.created_at DESC
                    LIMIT 1
                ");
                $msgStmt->bindParam(':chatroom_id', $row['id']);
                $msgStmt->execute();
                $lastMessage = $msgStmt->fetch(PDO::FETCH_ASSOC);

                $row['members'] = $members;
                $row['last_message'] = $lastMessage;
                $chatrooms[] = $row;
            }

            return $chatrooms;
        } catch (PDOException $e) {
            return [];
        }
    }

    public function getChatroomMessages($chatroomId, $offset = 0, $limit = 20) {
        try {
            $stmt = $this->db->prepare("
                SELECT m.*, u.username as sender_name
                FROM messages m
                LEFT JOIN users u ON m.sender_id = u.id
                WHERE m.chatroom_id = :chatroom_id
                ORDER BY m.created_at DESC
                LIMIT :limit OFFSET :offset
            ");
            $stmt->bindParam(':chatroom_id', $chatroomId);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            return [];
        }
    }
}
?>
```

### 3.3 PHP API实现

#### 3.3.1 登录API

`api/login.php`:
```php
<?php
header('Content-Type: application/json');
require_once '../includes/auth.php';

// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只处理POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['code' => 405, 'msg' => '方法不允许']);
    exit;
}

// 获取POST数据
$data = json_decode(file_get_contents('php://input'), true);
$username = isset($data['username']) ? trim($data['username']) : '';

if (empty($username)) {
    http_response_code(400);
    echo json_encode(['code' => 400, 'msg' => '用户名不能为空']);
    exit;
}

// 处理登录
$auth = new Auth();
$result = $auth->login($username);

if ($result) {
    // 设置会话
    session_start();
    $_SESSION['user_id'] = $result['user_id'];
    $_SESSION['username'] = $result['username'];

    echo json_encode([
        'code' => 200,
        'msg' => '登录成功',
        'data' => $result
    ]);
} else {
    http_response_code(500);
    echo json_encode(['code' => 500, 'msg' => '登录失败']);
}
?>
```

#### 3.3.2 聊天室API

`api/chatrooms.php`:
```php
<?php
header('Content-Type: application/json');
require_once '../includes/chat.php';
require_once '../includes/auth.php';

// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 会话验证
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['code' => 401, 'msg' => '未授权']);
    exit;
}

$userId = $_SESSION['user_id'];
$chatManager = new ChatManager();

// 获取用户的聊天室列表
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $chatrooms = $chatManager->getUserChatrooms($userId);

    echo json_encode([
        'code' => 200,
        'msg' => '成功',
        'data' => $chatrooms
    ]);
}
// 创建新聊天室
else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);

    // 验证数据
    if (!isset($data['name']) || !isset($data['members']) || !is_array($data['members'])) {
        http_response_code(400);
        echo json_encode(['code' => 400, 'msg' => '参数错误']);
        exit;
    }

    // 确保创建者在成员列表中
    if (!in_array($userId, $data['members'])) {
        $data['members'][] = $userId;
    }

    // 创建聊天室
    $chatroomId = $chatManager->createChatroom($data['name'], $data['members'], $userId);

    if ($chatroomId) {
        echo json_encode([
            'code' => 200,
            'msg' => '创建成功',
            'data' => ['id' => $chatroomId]
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['code' => 500, 'msg' => '创建失败']);
    }
}
?>
```

#### 3.3.3 消息API

`api/messages.php`:
```php
<?php
header('Content-Type: application/json');
require_once '../includes/chat.php';

// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 会话验证
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['code' => 401, 'msg' => '未授权']);
    exit;
}

$userId = $_SESSION['user_id'];
$chatManager = new ChatManager();

// 获取聊天室消息
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // 验证参数
    if (!isset($_GET['chatroom_id'])) {
        http_response_code(400);
        echo json_encode(['code' => 400, 'msg' => '缺少聊天室ID']);
        exit;
    }

    $chatroomId = $_GET['chatroom_id'];
    $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;

    // 获取消息
    $messages = $chatManager->getChatroomMessages($chatroomId, $offset, $limit);

    echo json_encode([
        'code' => 200,
        'msg' => '成功',
        'data' => [
            'messages' => $messages,
            'offset' => $offset,
            'limit' => $limit
        ]
    ]);
}
// 发送消息
else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);

    // 验证数据
    if (!isset($data['chatroom_id']) || !isset($data['content'])) {
        http_response_code(400);
        echo json_encode(['code' => 400, 'msg' => '参数错误']);
        exit;
    }

    // 通过FastAPI发送消息
    $fastApiUrl = "http://localhost:298/api/chat/send_message";
    $postData = [
        'chatroom_id' => $data['chatroom_id'],
        'msg' => $data['content'],
        'sender' => $userId
    ];

    $options = [
        'http' => [
            'header'  => "Content-type: application/json\r\n",
            'method'  => 'POST',
            'content' => json_encode($postData)
        ]
    ];

    $context  = stream_context_create($options);
    $result = file_get_contents($fastApiUrl, false, $context);

    if ($result !== false) {
        echo $result;
    } else {
        http_response_code(500);
        echo json_encode(['code' => 500, 'msg' => '发送失败']);
    }
}
?>
```

### 3.4 前端页面实现

#### 3.4.1 登录页面

`index.php`:
```php
<?php
session_start();

// 如果已登录，重定向到聊天页面
if (isset($_SESSION['user_id'])) {
    header('Location: chat.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天应用 - 登录</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="login-container">
        <h1>聊天应用</h1>
        <div class="login-form">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" placeholder="请输入用户名" required>
            </div>
            <button id="login-btn" class="btn btn-primary">登录</button>
        </div>
    </div>

    <script>
        document.getElementById('login-btn').addEventListener('click', async () => {
            const username = document.getElementById('username').value.trim();

            if (!username) {
                alert('请输入用户名');
                return;
            }

            try {
                const response = await fetch('api/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username })
                });

                const result = await response.json();

                if (result.code === 200) {
                    // 保存令牌到本地存储
                    localStorage.setItem('chat_token', result.data.token);
                    localStorage.setItem('chat_user', JSON.stringify({
                        id: result.data.user_id,
                        username: result.data.username
                    }));

                    // 重定向到聊天页面
                    window.location.href = 'chat.php';
                } else {
                    alert(result.msg || '登录失败');
                }
            } catch (error) {
                console.error('登录错误:', error);
                alert('登录失败，请重试');
            }
        });
    </script>
</body>
</html>
```

#### 3.4.2 聊天页面

`chat.php`:
```php
<?php
session_start();

// 如果未登录，重定向到登录页面
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}

$userId = $_SESSION['user_id'];
$username = $_SESSION['username'];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天应用</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="chat-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="user-info">
                <div class="avatar">
                    <img src="assets/img/default-avatar.png" alt="头像">
                </div>
                <div class="username"><?php echo htmlspecialchars($username); ?></div>
            </div>

            <div class="chat-actions">
                <button id="new-chat-btn" class="btn">新聊天</button>
                <button id="create-group-btn" class="btn">创建群组</button>
            </div>

            <div class="chat-list" id="chat-list">
                <!-- 聊天列表将通过JavaScript动态加载 -->
            </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-area">
            <div class="chat-header" id="chat-header">
                <div class="chat-title">选择一个聊天</div>
            </div>

            <div class="messages" id="messages">
                <div class="empty-state">
                    <p>选择一个聊天或开始新的对话</p>
                </div>
            </div>

            <div class="message-input-container" id="message-input-container" style="display: none;">
                <div class="message-tools">
                    <button class="tool-btn" id="image-btn" title="发送图片">
                        <img src="assets/img/image-icon.svg" alt="图片">
                    </button>
                    <button class="tool-btn" id="emoji-btn" title="表情">
                        <img src="assets/img/emoji-icon.svg" alt="表情">
                    </button>
                    <button class="tool-btn" id="card-btn" title="卡片消息">
                        <img src="assets/img/card-icon.svg" alt="卡片">
                    </button>
                </div>

                <textarea id="message-input" placeholder="输入消息..."></textarea>
                <button id="send-btn" class="btn btn-primary">发送</button>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div class="modal" id="new-chat-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新聊天</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <input type="text" id="search-user" placeholder="搜索用户...">
                <div id="search-results" class="search-results"></div>
            </div>
        </div>
    </div>

    <div class="modal" id="create-group-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>创建群组</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="group-name">群组名称</label>
                    <input type="text" id="group-name" placeholder="输入群组名称">
                </div>
                <div class="form-group">
                    <label>添加成员</label>
                    <input type="text" id="search-group-user" placeholder="搜索用户...">
                    <div id="group-search-results" class="search-results"></div>
                </div>
                <div class="selected-members" id="selected-members">
                    <div class="member-tag" data-id="<?php echo htmlspecialchars($userId); ?>">
                        <?php echo htmlspecialchars($username); ?> (你)
                    </div>
                </div>
                <button id="create-group-submit" class="btn btn-primary">创建群组</button>
            </div>
        </div>
    </div>

    <script>
        // 用户信息
        const currentUser = {
            id: "<?php echo htmlspecialchars($userId); ?>",
            username: "<?php echo htmlspecialchars($username); ?>"
        };

        // 令牌
        const token = localStorage.getItem('chat_token');

        // WebSocket连接
        let socket;
        let currentChatroom = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 连接WebSocket
            connectWebSocket();

            // 加载聊天列表
            loadChatrooms();

            // 绑定事件
            bindEvents();
        });

        // 连接WebSocket
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const host = window.location.hostname;
            const port = 298; // FastAPI端口

            socket = new WebSocket(`${protocol}//${host}:${port}/api/chat/connect_chat_with_token?token=${token}`);

            socket.onopen = () => {
                console.log('WebSocket连接已建立');
            };

            socket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };

            socket.onclose = (event) => {
                console.log('WebSocket连接已关闭', event.code, event.reason);
                // 尝试重新连接
                setTimeout(connectWebSocket, 3000);
            };

            socket.onerror = (error) => {
                console.error('WebSocket错误:', error);
            };
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(data) {
            const type = data.type;

            switch (type) {
                case 'private_message':
                case 'group_message':
                    handleNewMessage(data);
                    break;
                case 'system':
                    console.log('系统消息:', data.msg);
                    break;
                default:
                    console.log('未处理的消息类型:', type, data);
            }
        }

        // 加载聊天室列表
        async function loadChatrooms() {
            try {
                const response = await fetch('api/chatrooms.php');
                const result = await response.json();

                if (result.code === 200) {
                    renderChatList(result.data);
                } else {
                    console.error('加载聊天室失败:', result.msg);
                }
            } catch (error) {
                console.error('加载聊天室错误:', error);
            }
        }
    </script>
</body>
</html>
```

## 4. 部署方案

### 4.1 Nginx配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html/php_chat;

    # PHP处理
    location / {
        index index.php;
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }

    # FastAPI WebSocket处理
    location /api/chat/ {
        proxy_pass http://localhost:298;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 静态文件
    location /assets/ {
        expires 7d;
    }
}
```

### 4.2 启动服务

#### 4.2.1 启动FastAPI服务

```bash
cd /path/to/fastapi_ws_chat
uvicorn main:app --host 0.0.0.0 --port 298
```

#### 4.2.2 启动PHP服务

```bash
# 如果使用PHP内置服务器（开发环境）
cd /path/to/php_chat
php -S 0.0.0.0:80

# 生产环境使用Nginx+PHP-FPM
systemctl start nginx
systemctl start php7.4-fpm
```

## 5. 迁移步骤

### 5.1 准备工作

1. 备份当前FastAPI应用
2. 创建PHP应用目录结构
3. 配置共享数据库访问

### 5.2 分步实施

1. 修改FastAPI代码，添加令牌认证
2. 开发PHP基础功能（登录、聊天列表）
3. 实现PHP与FastAPI的通信
4. 测试WebSocket连接
5. 逐步迁移其他功能
6. 配置Nginx反向代理
7. 全面测试并上线