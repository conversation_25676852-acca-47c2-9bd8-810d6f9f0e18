<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天应用</title>
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <!-- 用户信息 -->
            <div class="user-profile">
                <div class="user-avatar">
                    <img id="user-avatar" src="/static/img/default-avatar.png" alt="头像">
                </div>
                <div class="user-info">
                    <div id="username">未登录</div>
                    <div class="user-status online">在线</div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button id="new-chat-btn">新聊天</button>
                <button id="new-group-btn">创建群组</button>
            </div>

            <!-- 聊天列表 -->
            <div class="chat-list" id="chat-list">
                <!-- 聊天项会动态添加 -->
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 聊天头部 -->
            <div class="chat-header">
                <div id="current-chat-name">选择一个聊天</div>
                <div class="chat-actions">
                    <button id="add-user-btn" style="display: none;">添加用户</button>
                </div>
            </div>

            <div class="chat-container">
                <!-- 聊天消息区 -->
                <div class="chat-messages-wrapper">
                    <!-- 加载更多按钮 -->
                    <div class="load-more-container">
                        <button id="load-more-btn" style="display: none;">加载更多消息</button>
                    </div>

                    <div class="chat-messages" id="chat-messages">
                        <div class="empty-state">
                            选择一个聊天或开始新的对话
                        </div>
                    </div>
                </div>

                <!-- 群组成员面板 -->
                <div class="group-members-panel" id="group-members-panel" style="display: none;">
                    <div class="panel-header">群组成员</div>
                    <div class="members-list" id="members-list">
                        <!-- 成员列表会动态添加 -->
                    </div>
                    <div class="panel-footer">
                        <button id="panel-add-member-btn">添加成员</button>
                    </div>
                </div>
            </div>

            <!-- 消息功能工具栏 -->
            <div class="message-toolbar">
                <div class="toolbar-buttons">
                    <button id="emoji-btn" class="toolbar-btn" title="表情" disabled>
                        <span class="toolbar-icon">&#128512;</span>
                    </button>
                    <button id="image-btn" class="toolbar-btn" title="图片" disabled>
                        <span class="toolbar-icon">&#128247;</span>
                        <input type="file" id="image-upload" accept="image/*" style="display: none;">
                    </button>
                    <button id="quick-reply-btn" class="toolbar-btn" title="快捷回复" disabled>
                        <span class="toolbar-icon">&#128172;</span>
                    </button>
                    <button id="card-message-btn" class="toolbar-btn" title="卡片消息" disabled>
                        <span class="toolbar-icon">&#128203;</span>
                    </button>
                    <button id="voice-call-btn" class="toolbar-btn" title="语音通话" disabled>
                        <span class="toolbar-icon">&#128222;</span>
                    </button>
                </div>
            </div>

            <!-- 消息输入区 -->
            <div class="message-input-container">
                <textarea id="message-input" placeholder="输入消息..." disabled></textarea>
                <button id="send-btn" disabled>发送</button>
            </div>

            <!-- 表情选择器 -->
            <div id="emoji-picker" class="emoji-picker">
                <div class="emoji-container">
                    <!-- 预设表情，也会通过JS动态添加更多 -->
                    <span class="emoji-item">😀</span>
                    <span class="emoji-item">😁</span>
                    <span class="emoji-item">😂</span>
                    <span class="emoji-item">😃</span>
                    <span class="emoji-item">😄</span>
                    <span class="emoji-item">😅</span>
                    <span class="emoji-item">😆</span>
                    <span class="emoji-item">😇</span>
                    <span class="emoji-item">😈</span>
                    <span class="emoji-item">😉</span>
                    <span class="emoji-item">😊</span>
                    <span class="emoji-item">😋</span>
                    <span class="emoji-item">😌</span>
                    <span class="emoji-item">😍</span>
                    <span class="emoji-item">😎</span>
                    <span class="emoji-item">😏</span>
                    <span class="emoji-item">😐</span>
                    <span class="emoji-item">😑</span>
                    <span class="emoji-item">😒</span>
                    <span class="emoji-item">😓</span>
                </div>
            </div>

            <!-- 快捷回复面板 -->
            <div id="quick-reply-panel" class="quick-reply-panel">
                <div class="quick-reply-container">
                    <div class="quick-reply-item" data-text="你好，有什么可以帮助你的吗？">你好，有什么可以帮助你的吗？</div>
                    <div class="quick-reply-item" data-text="好的，我稍后回复你">好的，我稍后回复你</div>
                    <div class="quick-reply-item" data-text="谢谢你的信息">谢谢你的信息</div>
                    <div class="quick-reply-item" data-text="收到，我会尽快处理">收到，我会尽快处理</div>
                    <div class="quick-reply-item" data-text="这个问题我需要进一步了解">这个问题我需要进一步了解</div>
                </div>
            </div>

            <!-- 卡片消息面板 -->
            <div id="card-message-panel" class="card-message-panel"></div>
        </div>
    </div>

    <!-- 模态框 - 新建聊天 -->
    <div class="modal" id="new-chat-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新建聊天</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="search-container">
                    <input type="text" id="user-search" placeholder="搜索用户...">
                    <button id="search-btn">搜索</button>
                </div>
                <div class="search-results" id="user-search-results">
                    <!-- 搜索结果会动态添加 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 - 创建群组 -->
    <div class="modal" id="new-group-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>创建群组</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="group-name">群组名称</label>
                    <input type="text" id="group-name" placeholder="输入群组名称">
                </div>
                <div class="form-group">
                    <label for="group-members">添加成员</label>
                    <div class="search-container">
                        <input type="text" id="group-user-search" placeholder="搜索用户...">
                        <button id="group-search-btn">搜索</button>
                    </div>
                    <div class="search-results" id="group-user-search-results">
                        <!-- 搜索结果会动态添加 -->
                    </div>
                </div>
                <div class="selected-members" id="selected-members">
                    <!-- 已选成员会动态添加 -->
                </div>
                <div class="form-actions">
                    <button id="create-group-btn">创建群组</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录表单 -->
    <div class="login-container" id="login-container">
        <div class="login-form">
            <h2>登录聊天</h2>
            <div class="form-group">
                <label for="login-username">用户名</label>
                <input type="text" id="login-username" placeholder="输入用户名">
            </div>
            <div class="form-actions">
                <button id="login-btn">登录</button>
            </div>
        </div>
    </div>

    <!-- 图片预览模态框 -->
    <div id="image-preview-modal" class="image-preview-modal">
        <span class="image-preview-close">&times;</span>
        <img class="image-preview-content" id="preview-image" src="" alt="图片预览">
    </div>

    <!-- 卡片编辑模态框 -->
    <div class="modal" id="card-edit-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="card-edit-title">编辑卡片消息</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div id="card-form-container"></div>
            </div>
            <div class="modal-footer">
                <button id="send-card-btn">发送</button>
                <button id="cancel-card-btn">取消</button>
            </div>
        </div>
    </div>

    <script type="module" src="/static/js/app.js"></script>
</body>
</html>