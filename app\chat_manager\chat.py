from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException
from app.chat_manager.server import ConnectionManager
from pydantic import BaseModel, Field, validator
from utils.redis_util import get_redis
from app.database import get_db, generate_id
from app.models import User, ChatRoom, Message
from app.utils.message_filter import MessageFilter
from sqlalchemy.orm import Session
from sqlalchemy import select  # 保留select用于其他可能的查询
from typing import List, Optional
import json

app = APIRouter()

cm = ConnectionManager()


@app.websocket("/connect_chat")
async def connect_chat(websocket: WebSocket, user_code: str, db: Session = Depends(get_db)):
    try:
        await cm.connect(websocket, user_code, db)
    except WebSocketDisconnect:
        # 连接断开时移除连接
        await cm.disconnect(user_code)
    except Exception as e:
        # 处理其他异常
        print(f"WebSocket连接异常: {str(e)}")
        # 确保连接被清理
        await cm.disconnect(user_code)


class ChatMessageModel(BaseModel):
    msg: str = Field(..., title="消息内容")
    sender: str = Field(..., title="发送者ID")
    recipient: Optional[str] = Field(None, title="接收者ID")
    chatroom_id: Optional[str] = Field(None, title="聊天室ID")

    @validator('msg')
    def validate_message_content(cls, v, values):
        # 检查消息是否包含手机号码
        # 如果有sender，就传入用户ID
        sender_id = values.get('sender') if values else None
        contains_phone, filtered_content, _ = MessageFilter.filter_phone_number(v, sender_id)
        if contains_phone:
            # 如果包含手机号码，返回过滤后的内容
            return filtered_content
        return v


@app.post("/send_message", summary="发送消息")
async def send_message(param: ChatMessageModel, db: Session = Depends(get_db), r=Depends(get_redis)):
    """发送消息到聊天室或私聊"""

    if param.chatroom_id:
        # 发送到聊天室
        ws_param = {
            "type": "group_message",
            "msg": param.msg,
            "sender": param.sender,
            "chatroom_id": param.chatroom_id
        }
    else:
        # 私聊消息
        ws_param = {
            "type": "private_message",
            "msg": param.msg,
            "sender": param.sender,
            "recipient": param.recipient
        }

    # 进行消息发布
    await r.publish('chat', json.dumps(ws_param))

    return {'code': 200, 'msg': '成功', 'data': ''}


class EditMessageModel(BaseModel):
    message_id: str = Field(..., title="消息ID")
    content: str = Field(..., title="新消息内容")
    user_id: str = Field(..., title="用户ID")


@app.post("/edit_message", summary="编辑消息")
async def edit_message(param: EditMessageModel, db: Session = Depends(get_db), r=Depends(get_redis)):
    """编辑消息"""

    ws_param = {
        "type": "edit_message",
        "message_id": param.message_id,
        "content": param.content,
        "sender": param.user_id
    }

    await r.publish('chat', json.dumps(ws_param))

    return {'code': 200, 'msg': '成功', 'data': ''}


class CreateGroupModel(BaseModel):
    name: str = Field(..., title="群聊名称")
    creator_id: str = Field(..., title="创建者ID")
    members: List[str] = Field(default=[], title="成员ID列表")


@app.post("/create_group", summary="创建群聊")
async def create_group(param: CreateGroupModel, db: Session = Depends(get_db), r=Depends(get_redis)):
    """创建群聊"""

    ws_param = {
        "type": "create_group",
        "name": param.name,
        "members": param.members,
        "sender": param.creator_id
    }

    await r.publish('chat', json.dumps(ws_param))

    return {'code': 200, 'msg': '成功', 'data': ''}


@app.get("/chatrooms/{user_id}", summary="获取用户的聊天室列表")
async def get_chatrooms(user_id: str, db: Session = Depends(get_db)):
    """获取用户的聊天室列表"""

    # 使用原生MySQL语法查询用户
    user_query = "SELECT * FROM users WHERE id = :user_id"
    user_result = db.execute(user_query, {"user_id": user_id}).first()
    if not user_result:
        raise HTTPException(status_code=404, detail="用户不存在")

    # 使用原生MySQL语法查询用户的聊天室
    query = """
        SELECT c.*
        FROM chatrooms c
        JOIN user_chatroom uc ON c.id = uc.chatroom_id
        WHERE uc.user_id = :user_id
    """
    result = db.execute(query, {"user_id": user_id})
    chatrooms = []

    # 处理查询结果
    for row in result:
        # 获取聊天室对象
        chatroom = ChatRoom(
            id=row.id,
            name=row.name,
            is_group=row.is_group,
            created_at=row.created_at
        )

        # 获取聊天室成员
        members_query = """
            SELECT u.*
            FROM users u
            JOIN user_chatroom uc ON u.id = uc.user_id
            WHERE uc.chatroom_id = :chatroom_id
        """
        members_result = db.execute(members_query, {"chatroom_id": chatroom.id})
        members = []
        for member_row in members_result:
            user = User(
                id=member_row.id,
                username=member_row.username,
                avatar=member_row.avatar
            )
            members.append(user)

        # 获取最后一条消息
        last_message_query = """
            SELECT m.*, u.username as sender_name
            FROM messages m
            LEFT JOIN users u ON m.sender_id = u.id
            WHERE m.chatroom_id = :chatroom_id
            ORDER BY m.created_at DESC
            LIMIT 1
        """
        last_message_result = db.execute(last_message_query, {"chatroom_id": chatroom.id}).first()

        # 设置关系
        chatroom.members = members
        if last_message_result:
            message = Message(
                id=last_message_result.id,
                content=last_message_result.content,
                created_at=last_message_result.created_at,
                updated_at=last_message_result.updated_at,
                is_edited=last_message_result.is_edited,
                is_html=last_message_result.is_html,
                message_type=last_message_result.message_type,
                content_type=last_message_result.content_type,
                sender_id=last_message_result.sender_id,
                chatroom_id=last_message_result.chatroom_id
            )
            # 设置sender_name
            if hasattr(last_message_result, 'sender_name'):
                message.sender = User(id=last_message_result.sender_id, username=last_message_result.sender_name)
            chatroom.messages = [message]

        chatrooms.append(chatroom)

    return {
        'code': 200,
        'msg': '成功',
        'data': [chatroom.to_dict() for chatroom in chatrooms]
    }


@app.get("/messages/{chatroom_id}", summary="获取聊天室的消息历史")
async def get_messages(chatroom_id: str, offset: int = 0, limit: int = 20, db: Session = Depends(get_db)):
    """获取聊天室的消息历史"""

    # 使用原生MySQL语法查询聊天室
    chatroom_query = "SELECT * FROM chatrooms WHERE id = :chatroom_id"
    chatroom_result = db.execute(chatroom_query, {"chatroom_id": chatroom_id}).first()
    if not chatroom_result:
        raise HTTPException(status_code=404, detail="聊天室不存在")

    # 使用原生MySQL语法查询消息
    messages_query = """
        SELECT m.*, u.username as sender_name
        FROM messages m
        LEFT JOIN users u ON m.sender_id = u.id
        WHERE m.chatroom_id = :chatroom_id
        ORDER BY m.created_at DESC
        LIMIT :limit OFFSET :offset
    """
    messages_result = db.execute(messages_query, {
        "chatroom_id": chatroom_id,
        "limit": limit,
        "offset": offset
    })

    # 处理消息结果
    messages = []
    for row in messages_result:
        message = Message(
            id=row.id,
            content=row.content,
            created_at=row.created_at,
            updated_at=row.updated_at,
            is_edited=row.is_edited,
            is_html=row.is_html,
            message_type=row.message_type,
            content_type=row.content_type,
            sender_id=row.sender_id,
            chatroom_id=row.chatroom_id
        )
        # 设置sender_name
        if hasattr(row, 'sender_name'):
            message.sender = User(id=row.sender_id, username=row.sender_name)
        messages.append(message)

    # 使用原生MySQL语法获取消息总数
    count_query = "SELECT COUNT(*) as total FROM messages WHERE chatroom_id = :chatroom_id"
    total_count = db.execute(count_query, {"chatroom_id": chatroom_id}).scalar_one()

    return {
        'code': 200,
        'msg': '成功',
        'data': {
            'messages': [msg.to_dict() for msg in messages],
            'total': total_count,
            'offset': offset,
            'limit': limit
        }
    }


@app.get("/users/search", summary="搜索用户")
async def search_users(keyword: str, db: Session = Depends(get_db)):
    """搜索用户"""

    stmt = select(User).filter(User.username.like(f"%{keyword}%"))
    users = db.execute(stmt).scalars().all()

    return {
        'code': 200,
        'msg': '成功',
        'data': [
            {
                'id': user.id,
                'username': user.username,
                'avatar': user.avatar
            }
            for user in users
        ]
    }


@app.get("/connection/stats", summary="获取WebSocket连接状态")
async def get_connection_stats():
    """获取当前WebSocket连接状态的统计信息"""
    stats = cm.get_connection_stats()
    return {
        'code': 200,
        'msg': '成功',
        'data': stats
    }
