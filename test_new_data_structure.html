<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新数据结构测试</title>
    <link rel="stylesheet" href="static/css/style.css">
</head>
<body>
    <div class="app-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="user-profile">
                <div class="user-avatar">
                    <img src="static/img/default-avatar.png" alt="用户头像">
                </div>
                <div class="user-info">
                    <div class="user-name">测试用户</div>
                    <div class="user-status online">在线</div>
                </div>
            </div>

            <div class="action-buttons">
                <button id="create-group-btn">创建群组</button>
                <button id="add-user-btn">添加用户</button>
            </div>

            <div class="chat-list" id="chat-list">
                <!-- 聊天列表将在这里动态生成 -->
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <div class="chat-header">
                <div id="current-chat-name">请选择一个聊天</div>
                <button id="add-user-btn" style="display: none;">添加用户</button>
            </div>

            <div class="chat-container">
                <div class="chat-messages-wrapper">
                    <div class="load-more-container" id="load-more-container" style="display: none;">
                        <button id="load-more-btn">加载更多消息</button>
                    </div>
                    <div class="chat-messages" id="chat-messages">
                        <div class="empty-state">请从左侧选择一个聊天</div>
                    </div>
                </div>

                <div class="group-members-panel" id="group-members-panel" style="display: none;">
                    <div class="panel-header">群组成员</div>
                    <div class="members-list" id="members-list"></div>
                </div>
            </div>

            <div class="message-toolbar">
                <div class="toolbar-buttons">
                    <button class="toolbar-btn" id="emoji-btn" disabled>
                        <span class="toolbar-icon">😊</span>
                    </button>
                    <button class="toolbar-btn" id="image-btn" disabled>
                        <span class="toolbar-icon">📷</span>
                    </button>
                    <button class="toolbar-btn" id="quick-reply-btn" disabled>
                        <span class="toolbar-icon">⚡</span>
                    </button>
                </div>
            </div>

            <div class="message-input-container">
                <textarea id="message-input" placeholder="输入消息..." disabled></textarea>
                <button id="send-btn" disabled>发送</button>
            </div>
        </div>
    </div>

    <script type="module">
        // 导入必要的模块
        import { setCurrentUser } from './static/js/modules/user.js';

        // 模拟未读计数功能
        window.unreadCounts = {};
        window.getUnreadCount = (chatroomId) => window.unreadCounts[chatroomId] || 0;

        // 模拟新的数据结构
        const newDataStructure = {
            "code": 0,
            "msg": "ok",
            "data": {
                "records": [
                    {
                        "id": 57504,
                        "title": "机器人建模",
                        "admin_name": "limin",
                        "userid": 2880191,
                        "chatrooms": [
                            {
                                "id": 94,
                                "name": "聊天室2",
                                "is_group": 1,
                                "created_at": "2025-05-23 17:20:28",
                                "pivot": {"task_id": 57504, "chat_id": 94, "status": 1},
                                "last_message": {
                                    "id": 1109,
                                    "content": "测试3",
                                    "created_at": "2025-05-23 17:27:36",
                                    "updated_at": "2025-05-23 17:27:38",
                                    "is_edited": null,
                                    "sender_id": "6559572",
                                    "chatroom_id": 94,
                                    "is_html": 0,
                                    "message_type": null,
                                    "content_type": null
                                },
                                "members": [
                                    {"chatroom_id": 94, "user_id": "3229571", "user_type": "1", "user_type_name": "管理员"},
                                    {"chatroom_id": 94, "user_id": "23233", "user_type": "2", "user_type_name": "雇主"},
                                    {"chatroom_id": 94, "user_id": "234554", "user_type": "3", "user_type_name": "设计师"},
                                    {"chatroom_id": 94, "user_id": "65654", "user_type": "4", "user_type_name": "普通成员"}
                                ]
                            },
                            {
                                "id": 93,
                                "name": "聊天室1",
                                "is_group": 1,
                                "created_at": "2025-05-23 17:20:01",
                                "pivot": {"task_id": 57504, "chat_id": 93, "status": 1},
                                "last_message": {
                                    "id": 1107,
                                    "content": "测试1",
                                    "created_at": "2025-05-23 17:26:33",
                                    "updated_at": "2025-05-23 17:26:36",
                                    "is_edited": null,
                                    "sender_id": "6229571",
                                    "chatroom_id": 93,
                                    "is_html": 0,
                                    "message_type": null,
                                    "content_type": null
                                },
                                "members": [
                                    {"chatroom_id": 93, "user_id": "3229571", "user_type": "1", "user_type_name": "管理员"},
                                    {"chatroom_id": 93, "user_id": "12", "user_type": "2", "user_type_name": "雇主"},
                                    {"chatroom_id": 93, "user_id": "995580", "user_type": "3", "user_type_name": "设计师"},
                                    {"chatroom_id": 93, "user_id": "65985", "user_type": "4", "user_type_name": "普通成员"},
                                    {"chatroom_id": 93, "user_id": "56563", "user_type": "4", "user_type_name": "普通成员"},
                                    {"chatroom_id": 93, "user_id": "4656", "user_type": "4", "user_type_name": "普通成员"}
                                ]
                            }
                        ]
                    },
                    {
                        "id": 60063,
                        "title": "1212",
                        "admin_name": "limin",
                        "userid": 2880191,
                        "chatrooms": []
                    },
                    {
                        "id": 60088,
                        "title": "33333333",
                        "admin_name": "limin",
                        "userid": 2880191,
                        "chatrooms": []
                    }
                ],
                "page": 1,
                "pageSize": 10
            }
        };

        // 导入聊天模块
        import { setChatrooms } from './static/js/modules/chat-test.js';

        // 模拟用户数据
        const testUser = {
            id: "3229571",
            username: "测试用户",
            avatar: "static/img/default-avatar.png"
        };

        // 初始化聊天列表
        document.addEventListener('DOMContentLoaded', () => {
            console.log('测试新数据结构...');

            // 设置当前用户
            setCurrentUser(testUser);

            // 设置聊天室数据
            setChatrooms(newDataStructure.data);
        });
    </script>
</body>
</html>
