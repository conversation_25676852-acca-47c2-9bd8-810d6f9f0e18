"""
TRTC API 路由
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
import time
import hmac
import hashlib
import base64
import json
import zlib
from app.dependencies import get_current_user
import TLSSigAPIv2

router = APIRouter()

# TRTC应用信息（实际应用中应该从配置文件或环境变量中获取）
TRTC_APP_ID = '1600082053'  # 替换为您的TRTC应用ID
TRTC_APP_KEY = "1900624a7b7e8ae68a69f4ff4436fe887a3236e123c05a4dd45c4c84faef0cb7"  # 替换为您的TRTC应用密钥

@router.get("/get_user_sig")
async def get_user_sig(user_id: str) -> Dict[str, Any]:
# async def get_user_sig(user_id: str, current_user = Depends(get_current_user)) -> Dict[str, Any]:

    """
    获取TRTC UserSig
    """
    # 验证用户身份
    # if current_user["id"] != user_id:
        # raise HTTPException(status_code=403, detail="无权获取其他用户的UserSig")
    
    try:
        # 生成UserSig
        api = TLSSigAPIv2.TLSSigAPIv2(TRTC_APP_ID, TRTC_APP_KEY)
        user_sig = api.gen_sig(user_id)
        return {
            "sdkAppId": TRTC_APP_ID,
            "userId": user_id,
            "userSig": user_sig,
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成UserSig失败: {str(e)}")
