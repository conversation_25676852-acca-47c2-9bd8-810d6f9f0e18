<!DOCTYPE html>
<html>
<head>
    <title>WebSocket聊天测试</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .chat-box {
            flex: 1;
            border: 1px solid #ccc;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
        }
        .form-group {
            margin-bottom: 10px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
        }
        .system {
            background-color: #f1f1f1;
        }
        .private {
            background-color: #e3f2fd;
        }
    </style>
</head>
<body>
    <h1>WebSocket聊天测试</h1>
    
    <div class="container">
        <div class="left-panel">
            <div class="form-group">
                <label for="user-id">用户ID:</label>
                <input type="text" id="user-id" placeholder="输入用户ID">
            </div>
            
            <div class="form-group">
                <button id="connect-btn">连接</button>
                <button id="disconnect-btn" disabled>断开</button>
            </div>
            
            <hr>
            
            <div class="form-group">
                <label for="recipient-id">接收者ID:</label>
                <input type="text" id="recipient-id" placeholder="输入接收者ID">
            </div>
            
            <div class="form-group">
                <label for="message">消息:</label>
                <textarea id="message" rows="3" placeholder="输入消息"></textarea>
            </div>
            
            <div class="form-group">
                <button id="send-btn" disabled>发送消息</button>
            </div>
        </div>
        
        <div class="chat-box" id="chat-box"></div>
    </div>
    
    <script>
        let socket = null;
        
        // 获取DOM元素
        const userIdInput = document.getElementById('user-id');
        const recipientIdInput = document.getElementById('recipient-id');
        const messageInput = document.getElementById('message');
        const chatBox = document.getElementById('chat-box');
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        const sendBtn = document.getElementById('send-btn');
        
        // 添加消息到聊天框
        function addMessage(message, type) {
            const messageElement = document.createElement('div');
            messageElement.classList.add('message', type);
            messageElement.textContent = message;
            chatBox.appendChild(messageElement);
            chatBox.scrollTop = chatBox.scrollHeight;
        }
        
        // 连接WebSocket
        connectBtn.addEventListener('click', () => {
            const userId = userIdInput.value.trim();
            if (!userId) {
                alert('请输入用户ID');
                return;
            }
            
            // 创建WebSocket连接
            socket = new WebSocket(`ws://localhost:8000/api/chat/connect_chat?user_code=${userId}`);
            
            // 连接打开
            socket.onopen = () => {
                addMessage(`已连接到服务器 (用户ID: ${userId})`, 'system');
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                sendBtn.disabled = false;
            };
            
            // 接收消息
            socket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                let messageText = '';
                
                if (data.type === 'system') {
                    messageText = `系统: ${data.msg}`;
                    addMessage(messageText, 'system');
                } else if (data.type === 'private_message') {
                    messageText = `来自 ${data.sender}: ${data.msg}`;
                    addMessage(messageText, 'private');
                }
            };
            
            // 连接关闭
            socket.onclose = () => {
                addMessage('与服务器断开连接', 'system');
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                sendBtn.disabled = true;
            };
            
            // 连接错误
            socket.onerror = (error) => {
                addMessage(`连接错误: ${error.message}`, 'system');
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                sendBtn.disabled = true;
            };
        });
        
        // 断开连接
        disconnectBtn.addEventListener('click', () => {
            if (socket) {
                socket.close();
                socket = null;
            }
        });
        
        // 发送消息
        sendBtn.addEventListener('click', () => {
            const recipient = recipientIdInput.value.trim();
            const message = messageInput.value.trim();
            const sender = userIdInput.value.trim();
            
            if (!recipient || !message) {
                alert('请输入接收者ID和消息');
                return;
            }
            
            // 通过WebSocket发送
            const msgData = {
                type: 'private_message',
                recipient: recipient,
                msg: message,
                sender: sender
            };
            
            socket.send(JSON.stringify(msgData));
            addMessage(`发送给 ${recipient}: ${message}`, 'private');
            messageInput.value = '';
            
            // 也可以通过HTTP API发送
            /* 
            fetch('/api/chat/create_chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    msg: message,
                    sender: sender,
                    recipient: recipient
                })
            });
            */
        });
    </script>
</body>
</html> 