/**
 * 消息状态管理模块 - 处理未读消息计数和离线消息
 */

// 导入通知模块中的UI函数
import { moveChatToTop, updateUnreadBadge } from './notification.js';
// 导入WebSocket模块
import { sendMessage } from './websocket.js';
// 导入聊天模块中的getCurrentChatroom函数
import { getCurrentChatroom } from './chat.js';

// 存储未读消息计数
let unreadCounts = {};

/**
 * 处理未读消息计数
 * @param {Object} data - 未读消息计数数据
 */
export function handleUnreadCounts(data) {
    const counts = data.counts;
    unreadCounts = { ...unreadCounts, ...counts };

    // 更新UI中的未读消息计数
    for (const [chatroomId, count] of Object.entries(counts)) {
        updateChatroomUnreadCount(chatroomId, count);
    }

    // 更新总未读消息计数
    updateTotalUnreadCount();
}

/**
 * 处理离线消息
 * @param {Object} data - 离线消息数据
 */
export function handleOfflineMessages(data) {
    const { chatroom_id, messages } = data;

    // 将消息添加到本地存储
    if (window.messages && !window.messages[chatroom_id]) {
        window.messages[chatroom_id] = [];
    }

    // 如果window.messages不存在，创建一个空对象
    if (!window.messages) {
        window.messages = {};
        window.messages[chatroom_id] = [];
    }

    // 添加消息并去重
    for (const msg of messages) {
        const exists = window.messages[chatroom_id].some(m => m.id === msg.id);
        if (!exists) {
            window.messages[chatroom_id].push(msg);
        }
    }

    // 如果当前正在查看该聊天室，则显示消息，但不自动重置未读计数
    // 让用户看到未读消息数量，并手动点击聊天室来查看消息
    const currentChatroom = getCurrentChatroom();
    if (currentChatroom && currentChatroom.id === parseInt(chatroom_id)) {
        // 如果存在renderMessages函数，调用它
        if (typeof window.renderMessages === 'function') {
            window.renderMessages(chatroom_id);
        }

        // 不再自动重置未读计数，让用户手动点击聊天室
        // resetUnreadCount(chatroom_id);
    }

    // 更新聊天列表中的最后一条消息并将聊天室置顶
    if (messages.length > 0) {
        const lastMsg = messages[messages.length - 1];

        // 直接更新UI，不触发未读计数增加
        const chatItem = document.querySelector(`.chat-item[data-id="${chatroom_id}"]`);
        if (chatItem) {
            const lastMessageEl = chatItem.querySelector('.last-message');
            if (lastMessageEl) {
                // 更新最后一条消息文本
                let previewText = lastMsg.content;

                // 如果是HTML内容，提取纯文本
                if (lastMsg.is_html) {
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = lastMsg.content;
                    previewText = tempDiv.textContent || tempDiv.innerText || lastMsg.content;
                }

                // 根据消息类型设置不同的预览
                if (lastMsg.content_type === 'image') {
                    previewText = '[图片]';
                } else if (lastMsg.content_type === 'file') {
                    previewText = '[文件]';
                } else if (lastMsg.message_type && lastMsg.message_type.includes('call')) {
                    previewText = '[通话]';
                }

                // 限制预览长度
                if (previewText.length > 20) {
                    previewText = previewText.substring(0, 20) + '...';
                }

                lastMessageEl.textContent = previewText;
            }
        }

        // 确保聊天室被移动到顶部
        moveChatToTop(chatroom_id);
    }

    // 触发离线消息接收事件
    document.dispatchEvent(new CustomEvent('offline-messages-received', {
        detail: {
            chatroom_id,
            messages
        }
    }));
}

/**
 * 更新聊天室未读计数
 * @param {string|number} chatroomId - 聊天室ID
 * @param {number} count - 未读消息数量
 */
export function updateChatroomUnreadCount(chatroomId, count) {
    // 更新内存中的计数
    unreadCounts[chatroomId] = count;

    // 使用notification.js中的updateUnreadBadge更新UI
    updateUnreadBadge(chatroomId, count);

    // 更新总未读消息计数
    updateTotalUnreadCount();
}

/**
 * 更新总未读消息计数
 */
function updateTotalUnreadCount() {
    const totalCount = Object.values(unreadCounts).reduce((sum, count) => sum + count, 0);

    // 更新页面标题
    updatePageTitle(totalCount);

    // 更新导航栏未读计数
    const navBadge = document.querySelector('#nav-unread-badge');
    if (navBadge) {
        if (totalCount > 0) {
            navBadge.textContent = totalCount > 99 ? '99+' : totalCount;
            navBadge.style.display = 'block';
        } else {
            navBadge.style.display = 'none';
        }
    }
}

/**
 * 更新页面标题，显示未读消息数量
 * @param {number} count - 未读消息数量
 */
function updatePageTitle(count) {
    const originalTitle = document.title.replace(/^\(\d+\) /, '');
    if (count > 0) {
        document.title = `(${count}) ${originalTitle}`;
    } else {
        document.title = originalTitle;
    }
}


/**
 * 重置聊天室未读计数
 * @param {string|number} chatroomId - 聊天室ID
 */
export function resetUnreadCount(chatroomId) {
    // 更新内存中的计数
    unreadCounts[chatroomId] = 0;

    // 更新UI
    updateChatroomUnreadCount(chatroomId, 0);

    // 发送重置未读计数请求
    sendMessage({
        type: 'reset_unread_count',
        chatroom_id: chatroomId
    });
}

/**
 * 获取聊天室未读计数
 * @param {string|number} chatroomId - 聊天室ID
 * @returns {number} 未读消息数量
 */
export function getUnreadCount(chatroomId) {
    return unreadCounts[chatroomId] || 0;
}

/**
 * 获取总未读消息数量
 * @returns {number} 总未读消息数量
 */
export function getTotalUnreadCount() {
    return Object.values(unreadCounts).reduce((sum, count) => sum + count, 0);
}

// 监听WebSocket消息事件
document.addEventListener('DOMContentLoaded', () => {
    // 监听未读计数消息
    document.addEventListener('ws-message', (event) => {
        const data = event.detail;
        if (data.type === 'unread_counts') {
            handleUnreadCounts(data);
        } else if (data.type === 'offline_messages') {
            handleOfflineMessages(data);
        }
    });

    // 监听新消息事件，更新未读计数
    document.addEventListener('ws-message', (event) => {
        const data = event.detail;
        if (data.type === 'new_message' && data.message) {
            const message = data.message;
            const chatroomId = message.chatroom_id;

            // 如果不是当前聊天室，增加未读计数
            const currentChatroom = getCurrentChatroom();
            if (!currentChatroom || currentChatroom.id !== parseInt(chatroomId)) {
                const currentCount = unreadCounts[chatroomId] || 0;
                updateChatroomUnreadCount(chatroomId, currentCount + 1);

                // 向服务器发送更新未读计数请求，确保在线消息的未读计数也被持久化
                sendMessage({
                    type: 'update_unread_count',
                    chatroom_id: chatroomId,
                    count: currentCount + 1
                });

                // 记录日志，帮助调试
                console.log(`未读计数增加: 聊天室=${chatroomId}, 新计数=${currentCount + 1}`);
            }

            // 直接更新UI，不触发未读计数增加
            const chatItem = document.querySelector(`.chat-item[data-id="${chatroomId}"]`);
            if (chatItem) {
                const lastMessageEl = chatItem.querySelector('.last-message');
                if (lastMessageEl) {
                    // 更新最后一条消息文本
                    let previewText = message.content;

                    // 如果是HTML内容，提取纯文本
                    if (message.is_html) {
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = message.content;
                        previewText = tempDiv.textContent || tempDiv.innerText || message.content;
                    }

                    // 根据消息类型设置不同的预览
                    if (message.content_type === 'image') {
                        previewText = '[图片]';
                    } else if (message.content_type === 'file') {
                        previewText = '[文件]';
                    } else if (message.message_type && message.message_type.includes('call')) {
                        previewText = '[通话]';
                    }

                    // 限制预览长度
                    if (previewText.length > 20) {
                        previewText = previewText.substring(0, 20) + '...';
                    }

                    lastMessageEl.textContent = previewText;
                }
            }

            // 确保聊天室被移动到顶部
            moveChatToTop(chatroomId);
        }
    });
});
