/**
 * 测试用的聊天模块
 */
import { DEFAULT_AVATAR, MESSAGE_LIMIT, POLLING_INTERVAL, MAX_POLLING_ATTEMPTS } from './config-test.js';
import { renderChatListItem, updateChatItemLastMessage } from './notification-test.js';
import { resetUnreadCount, handleUnreadCounts, handleOfflineMessages, getUnreadCount } from './message-status-test.js';

// =============== 状态变量 ===============
let chatrooms = [];
let tasks = []; // 存储任务数据
let currentChatroom = null;
let messages = {};
let messageOffset = {};
let initialLoadComplete = {};
let selectedMembers = [];

// =============== 通用工具函数 ===============

/**
 * HTML转义
 * @param {string} text - 要转义的文本
 * @returns {string} 转义后的文本
 */
function escapeHtml(text) {
    if (!text) return '';

    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };

    return text.replace(/[&<>"']/g, m => map[m]);
}

/**
 * 格式化时间
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(date) {
    const now = new Date();
    const isToday = now.toDateString() === date.toDateString();

    if (isToday) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

// =============== 聊天室数据访问函数 ===============

/**
 * 获取当前聊天室
 * @returns {Object|null} 当前聊天室
 */
export function getCurrentChatroom() {
    return currentChatroom;
}

/**
 * 获取所有聊天室
 * @returns {Array} 聊天室数组
 */
export function getAllChatrooms() {
    return chatrooms;
}

/**
 * 获取聊天室消息
 * @param {string} chatroomId - 聊天室ID
 * @returns {Array} 消息数组
 */
export function getChatroomMessages(chatroomId) {
    return messages[chatroomId] || [];
}

/**
 * 设置聊天室列表 - 兼容新的任务数据结构
 * @param {Array|Object} data - 聊天室数组或包含任务的数据对象
 */
export function setChatrooms(data) {
    // 检查是否是新的数据结构（包含任务）
    if (data && data.records && Array.isArray(data.records)) {
        // 新的数据结构：包含任务的嵌套结构
        tasks = data.records;
        chatrooms = extractChatroomsFromTasks(tasks);
    } else if (Array.isArray(data)) {
        // 旧的数据结构：直接的聊天室数组
        chatrooms = data;
        tasks = [];
    } else {
        console.warn("无效的聊天室数据格式:", data);
        chatrooms = [];
        tasks = [];
    }

    // 按照最后消息时间排序聊天室，最新的在前面
    chatrooms = chatrooms.sort((a, b) => {
        // 如果没有最后消息，则使用聊天室创建时间
        const timeA = a.last_message ? new Date(a.last_message.created_at) : new Date(a.created_at);
        const timeB = b.last_message ? new Date(b.last_message.created_at) : new Date(b.created_at);
        return timeB - timeA; // 降序排列，最新的在前面
    });
    renderChatList();
}

/**
 * 从任务数据中提取聊天室列表
 * @param {Array} taskList - 任务列表
 * @returns {Array} 聊天室数组
 */
function extractChatroomsFromTasks(taskList) {
    const allChatrooms = [];
    
    taskList.forEach(task => {
        if (task.chatrooms && Array.isArray(task.chatrooms)) {
            task.chatrooms.forEach(chatroom => {
                // 为聊天室添加任务信息
                chatroom.task_info = {
                    id: task.id,
                    title: task.title,
                    admin_name: task.admin_name,
                    userid: task.userid
                };
                allChatrooms.push(chatroom);
            });
        }
    });
    
    return allChatrooms;
}

/**
 * 获取聊天名称
 * @param {Object} chatroom - 聊天室对象
 * @returns {string} 聊天室显示名称
 */
export function getChatName(chatroom) {
    // 简化的测试实现
    if (chatroom.is_group) return chatroom.name;
    return '私聊';
}

// =============== UI渲染函数 ===============

/**
 * 渲染聊天列表，显示所有聊天室
 * 支持按任务分组显示聊天室
 */
export function renderChatList() {
    console.log("渲染聊天列表，聊天室数量:", chatrooms.length, "任务数量:", tasks.length);
    const chatList = document.getElementById('chat-list');
    if (!chatList) return;
    
    chatList.innerHTML = '';

    if (tasks.length > 0) {
        // 新的数据结构：按任务分组显示
        renderChatListByTasks();
    } else {
        // 旧的数据结构：直接显示聊天室列表
        renderChatListFlat();
    }
}

/**
 * 按任务分组渲染聊天列表
 */
function renderChatListByTasks() {
    const chatList = document.getElementById('chat-list');
    
    tasks.forEach(task => {
        // 创建任务标题
        if (task.chatrooms && task.chatrooms.length > 0) {
            const taskHeader = createTaskHeader(task);
            chatList.appendChild(taskHeader);

            // 渲染该任务下的聊天室
            task.chatrooms.forEach(chatroom => {
                const displayName = chatroom.is_group ? chatroom.name : getChatName(chatroom);
                const isActive = currentChatroom && currentChatroom.id === chatroom.id;

                // 从message-status.js获取未读计数
                const unreadCount = getUnreadCount(chatroom.id);
                const chatItem = renderChatListItem(
                    chatroom,
                    displayName,
                    isActive,
                    selectedChatroom => selectChatroom(selectedChatroom),
                    unreadCount
                );
                
                // 为聊天室项添加缩进样式
                chatItem.classList.add('chat-item-indented');
                chatList.appendChild(chatItem);
            });
        }
    });
}

/**
 * 扁平化渲染聊天列表（兼容旧数据结构）
 */
function renderChatListFlat() {
    const chatList = document.getElementById('chat-list');
    
    chatrooms.forEach(chatroom => {
        const displayName = chatroom.is_group ? chatroom.name : getChatName(chatroom);
        const isActive = currentChatroom && currentChatroom.id === chatroom.id;

        // 从message-status.js获取未读计数
        const unreadCount = getUnreadCount(chatroom.id);
        const chatItem = renderChatListItem(
            chatroom,
            displayName,
            isActive,
            selectedChatroom => selectChatroom(selectedChatroom),
            unreadCount
        );
        chatList.appendChild(chatItem);
    });
}

/**
 * 创建任务标题元素
 * @param {Object} task - 任务对象
 * @returns {HTMLElement} 任务标题元素
 */
function createTaskHeader(task) {
    const taskHeader = document.createElement('div');
    taskHeader.className = 'task-header';
    taskHeader.innerHTML = `
        <div class="task-title">${task.title}</div>
        <div class="task-info">
            <span class="task-admin">管理员: ${task.admin_name}</span>
            <span class="task-chat-count">${task.chatrooms.length}个聊天室</span>
        </div>
    `;
    return taskHeader;
}

/**
 * 选择聊天室
 * @param {Object} chatroom - 聊天室对象
 */
export function selectChatroom(chatroom) {
    if (!chatroom) {
        console.error("尝试选择无效的聊天室");
        return;
    }

    console.log("选择聊天室:", chatroom.id, chatroom.is_group ? chatroom.name : getChatName(chatroom));
    currentChatroom = chatroom;

    // 更新当前聊天名称
    const currentChatName = document.getElementById('current-chat-name');
    if (currentChatName) {
        currentChatName.textContent = chatroom.is_group ? chatroom.name : getChatName(chatroom);
    }

    // 更新聊天列表UI激活状态
    updateChatListActiveState(chatroom.id);
    
    // 显示聊天室信息
    displayCurrentChatInfo(chatroom.id);
}

/**
 * 更新聊天列表UI激活状态
 * @param {string} activeChatroomId - 当前激活的聊天室ID
 */
function updateChatListActiveState(activeChatroomId) {
    // 移除所有聊天项的激活状态
    document.querySelectorAll('.chat-item').forEach(item => {
        item.classList.remove('active');
    });

    // 查找并激活当前聊天项
    const currentChatItem = document.querySelector(`.chat-item[data-id="${activeChatroomId}"]`);
    if (currentChatItem) {
        currentChatItem.classList.add('active');
        // 重置未读消息计数
        resetUnreadCount(activeChatroomId);
    }
}

/**
 * 显示当前聊天信息
 * @param {string} chatId - 聊天室ID
 */
export function displayCurrentChatInfo(chatId) {
    try {
        const chat = chatrooms.find(c => c.id === chatId);
        if (!chat) {
            console.warn("找不到聊天室:", chatId);
            return;
        }

        if (chat.is_group) {
            displayGroupMembers(chat);
        } else {
            // 对于私聊，隐藏成员面板
            const groupMembersPanel = document.getElementById('group-members-panel');
            if (groupMembersPanel) {
                groupMembersPanel.style.display = 'none';
            }
        }
    } catch (error) {
        console.error("显示聊天信息时出错:", error);
    }
}

/**
 * 显示群组成员信息
 * @param {Object} chat - 聊天室对象
 */
function displayGroupMembers(chat) {
    if (!chat.is_group) return;

    // 显示成员面板
    const groupMembersPanel = document.getElementById('group-members-panel');
    if (groupMembersPanel) {
        groupMembersPanel.style.display = 'flex';
    }

    // 获取成员数据（支持新旧数据结构）
    const members = getMembersData(chat);
    
    // 更新右侧成员列表
    const membersList = document.getElementById('members-list');
    if (membersList) {
        membersList.innerHTML = '';
        members.forEach(member => {
            const memberItem = document.createElement('div');
            memberItem.className = 'member-item';
            
            // 根据数据结构显示成员信息
            if (typeof member === 'object' && member.user_id) {
                // 新的数据结构：包含用户类型信息
                memberItem.innerHTML = `
                    <img src="${DEFAULT_AVATAR}" alt="头像" class="member-avatar">
                    <div class="member-info">
                        <div class="member-name">${member.user_id}</div>
                        <div class="member-type">${member.user_type_name || '成员'}</div>
                    </div>
                `;
            } else {
                // 旧的数据结构：只有用户ID
                memberItem.innerHTML = `
                    <img src="${DEFAULT_AVATAR}" alt="头像" class="member-avatar">
                    <div class="member-name">${member}</div>
                `;
            }
            
            membersList.appendChild(memberItem);
        });
    }
}

/**
 * 获取成员数据，兼容新旧数据结构
 * @param {Object} chat - 聊天室对象
 * @returns {Array} 成员数组
 */
function getMembersData(chat) {
    if (!chat.members) return [];
    
    // 检查是否是新的数据结构（包含用户类型信息）
    if (Array.isArray(chat.members) && chat.members.length > 0) {
        const firstMember = chat.members[0];
        if (typeof firstMember === 'object' && firstMember.user_id) {
            // 新的数据结构
            return chat.members;
        }
    }
    
    // 旧的数据结构：直接返回用户ID数组
    return chat.members;
}
