# FastAPI WebSocket聊天应用

基于FastAPI和WebSocket实现的实时聊天应用，支持私聊和群聊功能，具有丰富的消息类型和高可靠性的连接管理。

## 功能特点

### 基础聊天功能
- 用户登录和认证系统
- 实时消息收发（WebSocket）
- 私聊和群聊支持
- 聊天历史记录加载
- 消息编辑功能
- 群组创建和成员管理
- 用户搜索
- 显示聊天列表和最后一条消息

### 高级功能
- 多种消息类型支持（文本、图片、文件、卡片消息）
- 自定义卡片消息（任务详情卡片、报价单卡片、细节确认卡片）
- 卡片消息确认功能（数据库持久化）
- 离线消息队列和同步
- 未读消息计数
- 图片上传和预览
- 表情选择器
- 快捷回复功能
- 语音通话信号支持

### 系统特性
- WebSocket心跳机制（客户端和服务端双向）
- 断线自动重连（指数退避算法）
- 连接状态监控和恢复
- 页面可见性感知（优化后台页面性能）
- 消息发送失败重试机制
- 跨浏览器兼容性支持

## 技术栈

### 后端
- **FastAPI**: 高性能异步Web框架
- **WebSocket**: 实时通信协议
- **Redis**: 消息队列和发布订阅
- **MySQL**: 数据持久化
- **SQLAlchemy**: ORM数据库操作
- **Uvicorn**: ASGI服务器

### 前端
- **HTML5/CSS3**: 页面结构和样式
- **JavaScript (ES6+)**: 原生JS模块化开发
- **WebSocket API**: 客户端实时通信
- **IndexedDB**: 客户端离线消息存储
- **Fetch API**: 异步HTTP请求

## 安装和运行

### 前提条件
- Python 3.7+
- Redis服务器
- MySQL数据库

### 安装依赖
```bash
pip install -r requirements.txt
```

### 配置
修改`config/dev_config.toml`文件，配置Redis和MySQL连接参数：

```toml
[db_mysql]
host = "localhost"
port = 3306
user = "root"
password = "your_password"
db = "ws_chat"
charset = "utf8mb4"

[db_redis]
host = "localhost"
port = 6379
db = 0
password = ""
timeout = 10
```

### 运行应用
**方法1**: 使用uvicorn命令（开发模式）
```bash
uvicorn main:app --port 8000 --reload
```

**方法2**: 直接运行Python文件
```bash
python main.py
```

**方法3**: 使用Gunicorn部署（生产环境）
```bash
gunicorn main:app -c config/fastapi_config.py
```

应用默认在http://127.0.0.1:8000运行，生产环境在http://127.0.0.1:298运行。

### 注意事项
- 确保静态文件目录存在，代码会自动创建`static/img`, `static/css`, `static/js`目录
- 前端页面引用静态资源路径以`/static/`开头，例如`/static/css/style.css`
- 生产环境部署时，建议使用Nginx反向代理并配置WebSocket支持

## 使用方法

### 基本操作
1. 访问应用首页，输入用户名进行登录
2. 使用"新聊天"按钮搜索用户并开始私聊
3. 使用"创建群组"按钮创建一个新的群聊
4. 在聊天界面发送和接收消息
5. 双击自己发送的消息可以编辑
6. 点击"加载更多消息"可以查看更多历史消息

### 高级功能
1. **发送图片**: 点击图片按钮上传图片
2. **使用表情**: 点击表情按钮选择表情
3. **快捷回复**: 点击快捷回复按钮选择预设回复
4. **卡片消息**: 点击卡片消息按钮创建任务、报价或确认卡片
5. **确认卡片**: 点击卡片中的确认按钮进行确认（确认状态对所有用户可见）
6. **语音通话**: 点击语音通话按钮发起通话请求

## API文档

应用运行后，访问http://127.0.0.1:8000/docs可以查看API文档，包含以下主要API：

- `/api/chat/*`: 聊天相关API
- `/api/upload/*`: 文件上传API
- `/api/confirm-card`: 卡片确认API
- `/api/card-confirmations`: 获取卡片确认状态API

## WebSocket通信

WebSocket连接端点：`/api/chat/connect_chat?user_code={user_id}`

### 消息类型
- `private_message`: 私聊消息
- `group_message`: 群聊消息
- `image_message`: 图片消息
- `file_message`: 文件消息
- `heartbeat`: 心跳包
- `heartbeat_response`: 心跳响应
- `card_confirmation`: 卡片确认消息
- `system`: 系统消息

## 项目结构

```
├── app                     # 应用代码
│   ├── api                 # API路由
│   │   ├── card.py         # 卡片消息API
│   │   ├── trtc.py         # 实时通信API
│   │   └── upload.py       # 文件上传API
│   ├── chat_manager        # 聊天管理模块
│   │   ├── chat.py         # 聊天API路由
│   │   └── server.py       # WebSocket连接管理
│   ├── utils               # 应用工具
│   │   └── message_filter.py # 消息过滤器
│   ├── __init__.py
│   ├── database.py         # 数据库连接
│   └── models.py           # 数据模型
├── config                  # 配置文件
│   ├── dev_config.toml     # 开发环境配置
│   ├── fastapi_config.py   # Gunicorn配置
│   └── get_config.py       # 配置加载
├── static                  # 前端静态文件
│   ├── css
│   │   └── style.css       # 样式表
│   ├── js
│   │   ├── app.js          # 应用入口
│   │   └── modules/        # JS模块
│   │       ├── card-messages.js  # 卡片消息处理
│   │       ├── chat.js           # 聊天功能
│   │       ├── dom.js            # DOM操作
│   │       ├── events.js         # 事件处理
│   │       ├── image-handler.js  # 图片处理
│   │       ├── message-status.js # 消息状态
│   │       ├── message-tools.js  # 消息工具
│   │       ├── offline-queue.js  # 离线队列
│   │       ├── user.js           # 用户管理
│   │       ├── voice-call.js     # 语音通话
│   │       └── websocket.js      # WebSocket通信
│   ├── img                 # 图片资源
│   └── index.html          # 主页面
├── utils                   # 工具函数
│   ├── __init__.py
│   ├── log_util.py         # 日志工具
│   ├── redis_queue.py      # Redis队列
│   └── redis_util.py       # Redis工具
├── main.py                 # 应用入口
└── requirements.txt        # 依赖列表
```

## 系统架构

### 通信流程
1. 客户端通过WebSocket连接到服务器
2. 服务器维护活跃连接和用户状态
3. 消息通过Redis发布订阅机制分发
4. 服务器将消息推送给在线用户或存储为离线消息
5. 客户端重连时自动同步离线消息

### 心跳机制
- 客户端每30秒发送一次心跳包
- 服务器响应心跳并记录最后活动时间
- 服务器每30秒检查一次心跳超时（120秒）
- 超时连接自动断开并清理资源

