/**
 * 测试用的通知模块
 */
import { DEFAULT_AVATAR } from './config-test.js';
import { getUnreadCount } from './message-status-test.js';

/**
 * 渲染聊天列表项
 * @param {Object} chatroom - 聊天室对象
 * @param {string} displayName - 聊天室显示名称
 * @param {boolean} isActive - 是否为当前活动聊天室
 * @param {function} onClickCallback - 点击回调函数
 * @returns {HTMLElement} 聊天列表项元素
 */
export function renderChatListItem(chatroom, displayName, isActive, onClickCallback) {
    // 获取最后一条消息的显示文本
    const lastMessageText = formatLastMessage(chatroom.last_message);

    // 获取未读计数
    const unreadCount = getUnreadCount(chatroom.id);

    // 创建聊天列表项元素，传递聊天室对象以支持任务信息显示
    const chatItem = createChatItemElement(
        chatroom.id,
        displayName,
        lastMessageText,
        isActive,
        unreadCount,
        chatroom
    );

    // 绑定点击事件
    chatItem.addEventListener('click', () => {
        onClickCallback(chatroom);
    });

    return chatItem;
}

/**
 * 格式化最后一条消息
 * @param {Object} message - 消息对象
 * @returns {string} 格式化后的消息文本
 */
function formatLastMessage(message) {
    if (!message) return '无消息';

    // 检查是否为图片消息
    if (message.is_html && message.content.includes('<img')) {
        return '[图片消息]';
    }

    // 检查是否为卡片消息
    if (message.is_html && message.message_type && message.message_type.startsWith('card_')) {
        const cardType = message.message_type.replace('card_', '');
        switch (cardType) {
            case 'task':
                return '[任务详情卡片]';
            case 'quote':
                return '[报价单卡片]';
            case 'confirmation':
                return '[细节确认卡片]';
            default:
                return '[卡片消息]';
        }
    }

    return message.content;
}

/**
 * 创建聊天列表项元素
 * @param {string} chatroomId - 聊天室ID
 * @param {string} displayName - 显示名称
 * @param {string} lastMessageText - 最后一条消息文本
 * @param {boolean} isActive - 是否为活动聊天室
 * @param {number} unreadCount - 未读消息数量
 * @param {Object} chatroom - 聊天室对象（可选，用于显示任务信息）
 * @returns {HTMLElement} 聊天列表项元素
 */
function createChatItemElement(chatroomId, displayName, lastMessageText, isActive, unreadCount, chatroom = null) {
    const chatItem = document.createElement('div');
    chatItem.className = 'chat-item';
    chatItem.dataset.id = chatroomId;

    // 设置活动状态
    if (isActive) {
        chatItem.classList.add('active');
    }

    // 设置未读状态
    if (unreadCount > 0 && !isActive) {
        chatItem.classList.add('unread');
    }

    // 构建基本HTML结构
    let chatInfoHtml = `
        <div class="chat-name">${displayName}</div>
        <div class="chat-last-message">${lastMessageText}</div>
    `;

    // 如果有任务信息，添加任务标识
    if (chatroom && chatroom.task_info) {
        chatInfoHtml = `
            <div class="chat-name">
                ${displayName}
                <span class="task-indicator">[${chatroom.task_info.title}]</span>
            </div>
            <div class="chat-last-message">${lastMessageText}</div>
        `;
    }

    chatItem.innerHTML = `
        <div class="chat-avatar">
            <img src="${DEFAULT_AVATAR}" alt="头像">
        </div>
        <div class="chat-info">
            ${chatInfoHtml}
        </div>
        ${unreadCount > 0 && !isActive ? `<div class="unread-badge">${unreadCount}</div>` : ''}
    `;

    return chatItem;
}

/**
 * 更新聊天列表项的最后一条消息
 * @param {string} chatroomId - 聊天室ID
 * @param {Object} message - 消息对象
 */
export function updateChatItemLastMessage(chatroomId, message) {
    // 测试用空实现
}

/**
 * 将聊天室移动到聊天列表顶部
 * @param {string} chatroomId - 聊天室ID
 */
export function moveChatToTop(chatroomId) {
    // 测试用空实现
}
