/* 基础样式 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

button {
    cursor: pointer;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    background-color: #4a6ee0;
    color: white;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #3450b3;
}

button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

input, textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* 主布局 */
.app-container {
    display: flex;
    height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* 侧边栏样式 */
.sidebar {
    width: 300px;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #e1e1e1;
}

.user-profile {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e1e1e1;
}

.user-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-info {
    margin-left: 12px;
}

.user-status {
    font-size: 12px;
    color: #888;
}

.user-status.online {
    color: #4CAF50;
}

.action-buttons {
    display: flex;
    justify-content: space-between;
    padding: 10px 20px;
    border-bottom: 1px solid #e1e1e1;
}

.chat-list {
    flex: 1;
    overflow-y: auto;
}

.chat-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f1f1f1;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.chat-item:hover {
    background-color: #f9f9f9;
}

.chat-item.active {
    background-color: rgba(13, 110, 253, 0.15);
    box-shadow: inset 3px 0 0 #0d6efd;
}

.chat-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.chat-info {
    margin-left: 12px;
    flex: 1;
    overflow: hidden;
}

.chat-name {
    font-weight: bold;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-last-message {
    font-size: 13px;
    color: #888;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 主内容区样式 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e1e1e1;
}

#current-chat-name {
    font-weight: bold;
    font-size: 18px;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.empty-state {
    display: flex;
    height: 100%;
    justify-content: center;
    align-items: center;
    color: #aaa;
    font-size: 16px;
}

.message {
    margin-bottom: 15px;
    max-width: 70%;
    position: relative;
    clear: both;
}

.message-sent {
    margin-left: auto;
    margin-right: 10px;
}

.message-received {
    margin-right: auto;
    margin-left: 10px;
}

.message-group {
    display: flex;
    max-width: 85%;
    margin-bottom: 20px;
    width: 100%;
}

.message-current-user {
    background-color: rgba(74, 110, 224, 0.05);
    border-radius: 8px;
    padding: 5px;
}

.message-sender-avatar {
    margin-right: 10px;
}

.message-sender-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.message-bubble {
    flex: 1;
}

.message-sender-name {
    font-weight: bold;
    margin-bottom: 4px;
    color: #555;
}

.message-current-user .message-sender-name {
    color: #4a6ee0;
}

.message-content {
    padding: 10px 15px;
    border-radius: 18px;
    position: relative;
    word-break: break-word;
}

.message-sent .message-content {
    background-color: #4a6ee0;
    color: white;
    border-top-right-radius: 4px;
}

.message-received .message-content {
    background-color: #f1f1f1;
    border-top-left-radius: 4px;
}

.message-group .message-content {
    background-color: #f1f1f1;
    border-radius: 12px;
}

.message-current-user .message-content {
    border-left: 3px solid #4a6ee0;
}

.message-info {
    display: flex;
    justify-content: flex-start;
    font-size: 12px;
    color: #999;
    margin-top: 4px;
}

.message-sender {
    font-weight: bold;
}

.message-time {
    margin-left: 8px;
}

.message-edited {
    font-style: italic;
    margin-left: 8px;
}

.load-more-container {
    text-align: center;
    padding: 8px 0;
    position: sticky;
    top: 0;
    z-index: 50;
    background-color: rgba(255, 255, 255, 0.95);
    border-bottom: 1px solid #eee;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

#load-more-btn {
    background-color: #f5f5f5;
    color: #333;
    font-size: 13px;
    padding: 6px 12px;
    border-radius: 15px;
    border: 1px solid #ddd;
    transition: all 0.2s ease;
}

#load-more-btn:hover {
    background-color: #e5e5e5;
    transform: translateY(-1px);
    box-shadow: 0 2px 3px rgba(0,0,0,0.1);
}

/* 消息工具栏 */
.message-toolbar {
    display: flex;
    padding: 8px 15px;
    border-top: 1px solid #e1e1e1;
    background-color: #f9f9f9;
}

.toolbar-buttons {
    display: flex;
    gap: 10px;
}

.toolbar-btn {
    background: none;
    border: none;
    padding: 5px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.toolbar-btn:hover {
    background-color: #e0e0e0;
}

.toolbar-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: none;
}

.toolbar-icon {
    width: 24px;
    height: 24px;
}

/* 消息工具栏 */
.message-toolbar {
    display: flex;
    padding: 8px 15px;
    border-top: 1px solid #e1e1e1;
    background-color: #f9f9f9;
}

.toolbar-buttons {
    display: flex;
    gap: 10px;
}

.toolbar-btn {
    background: none;
    border: none;
    padding: 5px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.toolbar-btn:hover {
    background-color: #e0e0e0;
}

.toolbar-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: none;
}

.toolbar-icon {
    font-size: 24px;
    display: inline-block;
}

/* 消息输入区 */
.message-input-container {
    display: flex;
    padding: 15px;
    border-top: 1px solid #e1e1e1;
}

#message-input {
    flex: 1;
    height: 40px;
    resize: none;
    margin-right: 10px;
}

/* 表情选择器 */
.emoji-picker {
    display: none;
    position: fixed;
    background-color: white;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
    width: 300px;
    max-height: 200px;
    overflow-y: auto;
}

.emoji-container {
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
}

.emoji-item {
    font-size: 24px;
    padding: 5px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.emoji-item:hover {
    background-color: #f0f0f0;
    border-radius: 4px;
}

/* 快捷回复面板 */
.quick-reply-panel {
    display: none;
    position: fixed;
    background-color: white;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
    width: 300px;
    max-height: 200px;
    overflow-y: auto;
}

.quick-reply-container {
    display: flex;
    flex-direction: column;
    padding: 10px;
}

.quick-reply-item {
    padding: 8px 12px;
    margin-bottom: 5px;
    background-color: #f5f5f5;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-reply-item:hover {
    background-color: #e0e0e0;
}

/* 卡片消息面板 */
.card-message-panel {
    display: none;
    position: fixed;
    background-color: white;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 100;
    width: 300px;
    max-height: 200px;
    overflow-y: auto;
}

.card-message-container {
    display: flex;
    flex-direction: column;
    padding: 10px;
}

.card-type-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 5px;
    background-color: #f5f5f5;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.card-type-item:hover {
    background-color: #e0e0e0;
}

.card-icon {
    font-size: 24px;
    margin-right: 10px;
}

.card-label {
    font-size: 14px;
}

/* 图片消息样式 */
.message-image {
    max-width: 100%;
    max-height: 200px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    will-change: transform, opacity; /* 优化渲染性能 */
    transform: translateZ(0); /* 启用GPU加速 */
    backface-visibility: hidden; /* 减少闪烁 */
    -webkit-backface-visibility: hidden;
}

.message-image:hover {
    opacity: 0.95;
    transform: scale(1.02) translateZ(0);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 图片加载占位图样式 */
.message-image.loading {
    min-height: 100px;
    min-width: 100px;
    background-color: #f5f5f5;
    background-image: url('/static/img/image-placeholder.gif');
    background-position: center;
    background-repeat: no-repeat;
    background-size: 32px;
}

/* 图片错误样式 */
.message-image.error {
    min-height: 100px;
    min-width: 100px;
    background-color: #ffebee;
    border: 1px solid #ffcdd2;
}

/* 图片上传加载指示器 */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 8px;
    margin: 5px 0;
}

.loading-text {
    margin-right: 10px;
    color: #666;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #ccc;
    border-top-color: #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 错误消息样式 */
.error-message {
    padding: 10px;
    background-color: #ffebee;
    color: #d32f2f;
    border-radius: 8px;
    border-left: 4px solid #d32f2f;
    font-size: 14px;
}

/* 图片发送动画 */
@keyframes imageSendAnimation {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.message-image-sending {
    animation: imageSendAnimation 0.5s ease forwards;
}

/* 图片预览模态框 */
.image-preview-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-preview-modal.show {
    opacity: 1;
}

.image-preview-content {
    max-width: 90%;
    max-height: 90%;
    transform: scale(0.9);
    transition: transform 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    cursor: zoom-in;
    transform-origin: center center;
}

.image-preview-modal.show .image-preview-content {
    transform: scale(1);
}

.image-preview-close {
    position: absolute;
    top: 20px;
    right: 20px;
    color: white;
    font-size: 30px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.image-preview-close:hover {
    opacity: 1;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    width: 500px;
    border-radius: 8px;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e1e1e1;
}

.close-modal {
    cursor: pointer;
    font-size: 24px;
}

.modal-body {
    padding: 20px;
}

.search-container {
    display: flex;
    margin-bottom: 15px;
}

.search-container input {
    flex: 1;
    margin-right: 10px;
}

.search-results {
    max-height: 300px;
    overflow-y: auto;
}

.user-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #f1f1f1;
    cursor: pointer;
}

.user-item:hover {
    background-color: #f9f9f9;
}

.user-item img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-actions {
    text-align: right;
    margin-top: 20px;
}

.selected-members {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 15px;
}

.selected-member {
    display: flex;
    align-items: center;
    background-color: #f1f1f1;
    padding: 5px 10px;
    border-radius: 15px;
}

.selected-member img {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 5px;
}

.remove-member {
    margin-left: 5px;
    cursor: pointer;
    color: #999;
}

/* 登录表单 */
.login-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.login-form {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    width: 350px;
}

.login-form h2 {
    margin-bottom: 20px;
    text-align: center;
}

/* 添加群成员计数样式 */
.group-members-count {
    font-size: 12px;
    color: #666;
    font-weight: normal;
    margin-left: 5px;
}

/* 添加一个群成员列表显示 */
.group-members-list {
    margin-top: 5px;
    font-size: 12px;
    color: #666;
    max-height: 100px;
    overflow-y: auto;
    background-color: #f9f9f9;
    border-radius: 4px;
    padding: 5px;
    display: none;
}

.chat-header:hover .group-members-list {
    display: block;
}

/* 系统消息样式 */
.message-system {
    background-color: #f5f5f5;
    color: #666;
    text-align: center;
    margin: 10px auto;
    padding: 5px 10px;
    border-radius: 10px;
    font-size: 12px;
    max-width: 80%;
}

/* 聊天容器布局 */
.chat-container {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.chat-messages-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

/* 群组成员面板 */
.group-members-panel {
    width: 200px;
    border-left: 1px solid #e1e1e1;
    display: flex;
    flex-direction: column;
    background-color: #f9f9f9;
}

.panel-header {
    padding: 15px;
    font-weight: bold;
    border-bottom: 1px solid #e1e1e1;
    background-color: #f1f1f1;
}

.members-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.member-item {
    display: flex;
    align-items: center;
    padding: 8px;
    margin-bottom: 5px;
    border-radius: 4px;
}

.member-item:hover {
    background-color: #efefef;
}

.member-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 10px;
}

.member-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.panel-footer {
    padding: 10px;
    border-top: 1px solid #e1e1e1;
    text-align: center;
}

#panel-add-member-btn {
    width: 100%;
    padding: 8px;
}

/* 添加未读消息相关样式 */
.chat-item.unread {
    background-color: rgba(255, 193, 7, 0.1);
    box-shadow: inset 3px 0 0 #ffc107;
}

.chat-item.unread .chat-name,
.chat-item.unread .chat-last-message {
    font-weight: bold;
    color: #000;
}

/* 强制重绘类 - 仅用于360浏览器等特殊情况 */
.force-redraw {
    animation: force-redraw-animation 0.01s ease-in-out;
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

@keyframes force-redraw-animation {
    0% { transform: translateZ(0); }
    100% { transform: translateZ(0); }
}

/* 当未读消息和活动状态同时存在时，优先显示活动状态 */
.chat-item.active.unread {
    background-color: rgba(13, 110, 253, 0.15);
    box-shadow: inset 3px 0 0 #0d6efd;
}

.unread-badge {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #ffc107; /* 使用黄色，与未读状态相匹配 */
    color: #000; /* 黑色文字更易读 */
    border-radius: 50%;
    min-width: 20px;
    height: 20px;
    padding: 0 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* 当聊天项处于活动状态时，使用蓝色形式 */
.chat-item.active .unread-badge {
    background-color: #0d6efd;
    color: white;
}

/* 置顶动画 */
@keyframes highlight {
    0% {
        background-color: rgba(255, 193, 7, 0.3);
    }
    100% {
        background-color: transparent;
    }
}

.highlight-animation {
    animation: highlight 1.5s ease-out;
}

/* 消息淡入动画 */
@keyframes fadeIn {
    0% { opacity: 0; transform: translateY(10px); }
    100% { opacity: 1; transform: translateY(0); }
}

/* 当活动项有高亮动画时，使用不同的动画 */
@keyframes highlight-active {
    0% {
        background-color: rgba(13, 110, 253, 0.3);
    }
    100% {
        background-color: rgba(13, 110, 253, 0.15);
    }
}

/* 任务分组样式 */
.task-header {
    padding: 12px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-top: 1px solid #e9ecef;
    margin-top: 5px;
}

.task-header:first-child {
    margin-top: 0;
}

.task-title {
    font-weight: bold;
    font-size: 14px;
    color: #495057;
    margin-bottom: 4px;
}

.task-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #6c757d;
}

.task-admin {
    flex: 1;
}

.task-chat-count {
    font-weight: 500;
}

/* 缩进的聊天室项 */
.chat-item-indented {
    padding-left: 35px;
    background-color: #fdfdfe;
    border-left: 3px solid #e9ecef;
}

.chat-item-indented:hover {
    background-color: #f8f9fa;
}

.chat-item-indented.active {
    background-color: rgba(13, 110, 253, 0.1);
    border-left-color: #0d6efd;
}

/* 成员信息样式 */
.member-info {
    flex: 1;
    overflow: hidden;
}

.member-name {
    font-weight: 500;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.member-type {
    font-size: 11px;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 任务指示器样式 */
.task-indicator {
    font-size: 10px;
    color: #6c757d;
    font-weight: normal;
    margin-left: 5px;
    opacity: 0.8;
}

.chat-item.active.highlight-animation {
    animation: highlight-active 1.5s ease-out;
}

/* 语音通话模态框样式 */
.call-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1200;
    justify-content: center;
    align-items: center;
}

.call-modal-content {
    background-color: white;
    border-radius: 12px;
    width: 320px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.call-header {
    background-color: #4a6ee0;
    color: white;
    padding: 15px;
    text-align: center;
}

.call-header h3 {
    margin: 0;
    font-size: 18px;
}

.call-body {
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.call-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 15px;
    border: 2px solid #4a6ee0;
}

.call-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.call-status {
    margin-bottom: 15px;
    text-align: center;
    font-size: 16px;
    color: #333;
}

.call-timer {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #4a6ee0;
}

.call-controls {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.hangup-btn, .accept-btn, .reject-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 20px;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.hangup-btn {
    background-color: #e74c3c;
    color: white;
}

.accept-btn {
    background-color: #2ecc71;
    color: white;
}

.reject-btn {
    background-color: #e74c3c;
    color: white;
}

.hangup-btn:hover, .reject-btn:hover {
    background-color: #c0392b;
}

.accept-btn:hover {
    background-color: #27ae60;
}

.hangup-icon, .accept-icon, .reject-icon {
    font-size: 24px;
    margin-bottom: 5px;
}

/* 卡片样式 */
.card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 8px 0;
    overflow: hidden;
    background-color: #fff;
    max-width: 100%;
}

/* 卡片消息样式修复 */
.message-sent .message-content .card,
.message-received .message-content .card {
    margin: 0;
    box-shadow: none;
}

/* 确保私聊中发送方的卡片消息内容可见 */
.message-sent.card-message .message-content {
    background-color: #fff !important;
    color: #333 !important;
    border: 1px solid #4a6ee0;
    border-radius: 8px !important;
    padding: 0 !important;
    overflow: visible;
}

/* 确保私聊中接收方的卡片消息内容可见 */
.message-received.card-message .message-content {
    background-color: #fff !important;
    color: #333 !important;
    border: 1px solid #f1f1f1;
    border-radius: 8px !important;
    padding: 0 !important;
    overflow: visible;
}

/* 确保群聊中的卡片消息内容可见 */
.message-group.card-message .message-content {
    background-color: #fff !important;
    color: #333 !important;
    border: 1px solid #e1e1e1;
    border-radius: 8px !important;
    padding: 0 !important;
    overflow: visible;
}

.card-header {
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
    color: #333 !important;
}

/* 确保卡片头部所有文本可见 */
.card-header p,
.card-header span,
.card-header div,
.card-header h1,
.card-header h2,
.card-header h3,
.card-header h4,
.card-header h5,
.card-header h6 {
    color: #333 !important;
}

.card-body {
    padding: 16px;
    color: #333 !important;
}

/* 确保卡片内所有文本可见 */
.card-body p,
.card-body span,
.card-body div,
.card-body h1,
.card-body h2,
.card-body h3,
.card-body h4,
.card-body h5,
.card-body h6,
.card-body label,
.card-body li {
    color: #333 !important;
}

/* 任务卡片样式 */
.task-card .priority-high {
    background-color: #ff4d4f;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.task-card .priority-medium {
    background-color: #faad14;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.task-card .priority-low {
    background-color: #52c41a;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.task-card .status-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.task-card .status-pending {
    background-color: #faad14;
    color: white;
}

.task-card .status-inprogress {
    background-color: #1890ff;
    color: white;
}

.task-card .status-completed {
    background-color: #52c41a;
    color: white;
}

/* 报价单卡片样式 */
.quote-card table {
    width: 100%;
    border-collapse: collapse;
}

.quote-card th, .quote-card td {
    padding: 8px;
    border-bottom: 1px solid #eee;
    text-align: left;
}

.quote-card .total-value {
    font-weight: bold;
}

/* 确认卡片样式 */
.confirmation-card .confirmation-items {
    list-style: none;
    padding: 0;
}

.confirmation-card .confirmation-item {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.confirmation-card .confirmed {
    color: #52c41a;
}

.confirmation-card .pending {
    color: #faad14;
}

/* 项目行样式 */
.quote-item-row,
.confirmation-item-row {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
    padding: 8px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.remove-item-btn {
    background-color: #ff4d4f;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    cursor: pointer;
}

/* 卡片操作按钮样式 */
.card-actions {
    margin-top: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.view-task-btn {
    background-color: #1890ff;
    color: white;
    border: none;
}

.view-task-btn:hover {
    background-color: #40a9ff;
    color: white;
}

.confirm-quote-btn, .confirm-details-btn {
    background-color: #52c41a;
    color: white;
    border: none;
}

.confirm-quote-btn:hover, .confirm-details-btn:hover {
    background-color: #73d13d;
}

.confirm-quote-btn:disabled, .confirm-details-btn:disabled {
    background-color: #d9d9d9;
    cursor: not-allowed;
}

.confirmation-status {
    color: #52c41a;
    font-weight: 500;
}

.confirmation-status.confirmed {
    color: #52c41a;
}

/* 模态框底部样式 */
.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e1e1e1;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}