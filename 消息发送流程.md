# FastAPI WebSocket聊天应用 - 消息发送流程

本文档详细解释了FastAPI WebSocket聊天应用中消息发送的完整流程，特别是后端接收和处理消息的部分。

## 消息发送流程

### 1. 前端发送消息

前端通过WebSocket发送消息的关键代码位于 `static\js\modules\websocket.js` 文件中的 `sendMessage` 函数：

```javascript
// 将消息对象转换为JSON字符串
const messageStr = JSON.stringify(message);
// 发送消息
socket.send(messageStr);
```

这段代码将消息对象转换为JSON字符串，然后通过WebSocket连接发送到服务器。

### 2. 后端接收消息

后端接收消息的入口是 `app/chat_manager/chat.py` 文件中的 WebSocket 连接处理函数：

```python
@app.websocket("/connect_chat")
async def connect_chat(websocket: WebSocket, user_code: str, db: Session = Depends(get_db)):
    try:
        await cm.connect(websocket, user_code, db)
    except WebSocketDisconnect:
        # 连接断开时移除连接
        await cm.disconnect(user_code)
    except Exception as e:
        # 处理其他异常
        print(f"WebSocket连接异常: {str(e)}")
        # 确保连接被清理
        await cm.disconnect(user_code)
```

这个函数通过 `cm.connect()` 方法建立WebSocket连接，其中 `cm` 是 `ConnectionManager` 类的实例。

### 3. 消息处理核心

消息的实际处理发生在 `app/chat_manager/server.py` 文件中的 `ConnectionManager` 类中。当WebSocket连接建立后，系统会在后台监听消息：

```python
async def connect(self, websocket: WebSocket, user_id: str, db: Session):
    try:
        # 获取或创建用户
        user = db.execute(select(User).filter(User.id == user_id)).scalar_one_or_none()
        if not user:
            user = User(id=user_id, username=f"用户{user_id}")
            db.add(user)
            db.commit()
            db.refresh(user)

        # 添加到活跃用户列表
        self.active_users[user_id] = user

        # 添加连接并发送欢迎消息
        await websocket.accept()
        self.websocket_connections[user_id] = websocket

        # 记录初始心跳时间
        self.last_heartbeat[user_id] = time.time()

        # 发送欢迎消息
        await websocket.send_json({
            "type": "system",
            "msg": "欢迎来到沐风众包!",
            "sender": "系统消息",
            "recipient": user_id
        })

        # 监听消息
        while True:
            # 等待接收消息
            message = await websocket.receive_json()
            
            # 处理接收到的消息
            await self.handle_websocket_message(message, user_id, db)
            
    except WebSocketDisconnect:
        # 处理连接断开
        await self.disconnect(user_id)
    except Exception as e:
        # 处理其他异常
        print(f"WebSocket连接错误: {str(e)}")
        await self.disconnect(user_id)
```

关键部分是 `message = await websocket.receive_json()` 这行代码，它会等待并接收从客户端发送的JSON消息。一旦收到消息，就会调用 `handle_websocket_message` 方法进行处理。

### 4. 消息处理逻辑

`handle_websocket_message` 方法是消息处理的核心，它根据消息类型执行不同的操作：

```python
async def handle_websocket_message(self, message, user_id, db):
    """处理WebSocket消息"""
    # 获取消息类型
    msg_type = message.get("type", "")

    # 处理心跳消息
    if msg_type == "heartbeat":
        try:
            # 更新最后心跳时间
            current_time = time.time()
            self.last_heartbeat[user_id] = current_time

            # 获取客户端发送的时间戳
            client_timestamp = message.get("timestamp")

            # 收到心跳包，回复心跳响应
            sender_conn = self.websocket_connections.get(user_id)
            if sender_conn:
                await sender_conn.send_json({
                    "type": "heartbeat_response",
                    "client_timestamp": client_timestamp,
                    "server_timestamp": current_time,
                    "timestamp": datetime.now().timestamp()
                })
        except Exception as e:
            print(f"处理心跳消息错误: {str(e)}, 用户 {user_id}")
        return

    # 处理私聊消息
    if msg_type == "private_message":
        recipient_id = message.get("recipient")
        msg_content = message.get("msg")
        message_type = message.get("message_type", "")
        is_html = message.get("is_html", False)
        content_type = message.get("content_type", "text")
        client_timestamp = message.get("timestamp")

        # 创建私聊聊天室（如果不存在）
        chatroom = await self._get_or_create_private_chatroom(user_id, recipient_id, db)

        # 创建消息记录
        msg = Message(
            content=msg_content,
            sender_id=user_id,
            chatroom_id=chatroom.id,
            is_html=is_html,
            message_type=message_type,
            content_type=content_type
        )
        db.add(msg)
        db.commit()
        db.refresh(msg)

        # 构建消息对象，添加客户端时间戳
        message_dict = msg.to_dict()
        if client_timestamp:
            message_dict["client_timestamp"] = client_timestamp

        # 向接收者发送消息
        recipient_conn = self.websocket_connections.get(recipient_id)
        if recipient_conn:
            # 接收者在线，直接发送
            await recipient_conn.send_json({
                "type": "new_message",
                "message": message_dict
            })
        else:
            # 接收者离线，添加到Redis队列
            redis = await get_redis_instance()
            await RedisMessageQueue.add_offline_message(
                redis,
                recipient_id,
                msg.id,
                msg.chatroom_id
            )

        # 向发送者回复确认
        sender_conn = self.websocket_connections.get(user_id)
        if sender_conn:
            await sender_conn.send_json({
                "type": "message_sent",
                "message": message_dict
            })
```

这个方法会根据消息的 `type` 字段执行不同的处理逻辑：
- 对于 `heartbeat` 类型，更新心跳时间并回复心跳响应
- 对于 `private_message` 类型，创建或获取私聊聊天室，保存消息到数据库，然后发送给接收者
- 对于 `group_message` 类型，获取群聊信息，保存消息，然后发送给所有群成员
- 还有其他类型如 `create_group`、`edit_message` 等

### 5. Redis消息分发机制

除了直接通过WebSocket发送消息外，系统还使用Redis的发布订阅机制进行消息分发。这在 `main.py` 中实现：

```python
async def reader(channel):
    """处理Redis频道消息"""
    try:
        # 进行消息的消费
        async for msg in channel.listen():
            # 检查是否被取消
            if asyncio.current_task().cancelled():
                print("Redis消息监听任务被取消")
                break

            # 处理消息
            msg_data = msg.get("data")
            if msg_data and isinstance(msg_data, str):
                try:
                    msg_data_dict = json.loads(msg_data)
                    print(f"chat:{msg_data_dict}")
                    sender = msg_data_dict.get("sender")
                    
                    # 获取数据库会话
                    db = next(get_db())
                    try:
                        # 进行消息处理
                        await chat.cm.handle_websocket_message(msg_data_dict, sender, db)
                    except Exception as e:
                        print(f"处理Redis消息错误: {str(e)}, 消息类型: {msg_data_dict.get('type', 'unknown')}")
                        # 尝试回滚事务
                        if db:
                            try:
                                db.rollback()
                            except Exception as rollback_error:
                                print(f"回滚数据库事务错误: {str(rollback_error)}")
                    finally:
                        # 确保数据库会话被关闭
                        if db:
                            try:
                                db.close()
                            except Exception as close_error:
                                print(f"关闭数据库会话错误: {str(close_error)}")
```

这个机制允许通过HTTP API发送消息，然后通过Redis分发给WebSocket连接。例如，在 `app/chat_manager/chat.py` 中的 `send_message` API：

```python
@app.post("/send_message", summary="发送消息")
async def send_message(param: ChatMessageModel, db: Session = Depends(get_db), r=Depends(get_redis)):
    """发送消息到聊天室或私聊"""

    if param.chatroom_id:
        # 发送到聊天室
        ws_param = {
            "type": "group_message",
            "msg": param.msg,
            "sender": param.sender,
            "chatroom_id": param.chatroom_id
        }
    else:
        # 私聊消息
        ws_param = {
            "type": "private_message",
            "msg": param.msg,
            "sender": param.sender,
            "recipient": param.recipient
        }

    # 进行消息发布
    await r.publish('chat', json.dumps(ws_param))

    return {'code': 200, 'msg': '成功', 'data': ''}
```

## 总结

消息发送的完整流程是：

1. 前端通过 `socket.send(messageStr)` 发送JSON格式的消息
2. 后端在 `app/chat_manager/chat.py` 的 `connect_chat` 函数中接收连接
3. `app/chat_manager/server.py` 中的 `ConnectionManager.connect` 方法监听消息
4. 收到消息后，调用 `handle_websocket_message` 方法处理
5. 根据消息类型执行不同的处理逻辑（保存到数据库、转发给接收者等）
6. 对于HTTP API发送的消息，通过Redis发布订阅机制分发

这种设计使系统能够同时支持WebSocket直接通信和HTTP API发送消息，提高了系统的灵活性和可扩展性。
