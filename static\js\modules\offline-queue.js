/**
 * 离线消息队列模块 - 使用IndexedDB存储离线消息
 */

// 数据库名称和版本
const DB_NAME = 'chat_offline_db';
const DB_VERSION = 1;
const STORE_NAME = 'offline_messages';

// 打开数据库连接
function openDatabase() {
    return new Promise((resolve, reject) => {
        if (!window.indexedDB) {
            console.error('浏览器不支持IndexedDB');
            reject(new Error('浏览器不支持IndexedDB'));
            return;
        }
        
        const request = indexedDB.open(DB_NAME, DB_VERSION);
        
        request.onerror = (event) => {
            console.error('打开IndexedDB失败:', event.target.error);
            reject(event.target.error);
        };
        
        request.onsuccess = (event) => {
            resolve(event.target.result);
        };
        
        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            // 创建消息存储
            if (!db.objectStoreNames.contains(STORE_NAME)) {
                const store = db.createObjectStore(STORE_NAME, { keyPath: 'id', autoIncrement: true });
                store.createIndex('timestamp', 'timestamp', { unique: false });
                store.createIndex('chatroom_id', 'chatroom_id', { unique: false });
                store.createIndex('status', 'status', { unique: false });
                console.log('创建IndexedDB存储:', STORE_NAME);
            }
        };
    });
}

/**
 * 添加离线消息到队列
 * @param {Object} message - 消息对象
 * @returns {Promise<number>} 消息ID
 */
export async function addOfflineMessage(message) {
    try {
        // 避免重复添加心跳消息
        if (message.type === 'heartbeat') {
            return -1;
        }
        
        const db = await openDatabase();
        return new Promise((resolve, reject) => {
            const transaction = db.transaction([STORE_NAME], 'readwrite');
            const store = transaction.objectStore(STORE_NAME);
            
            // 添加时间戳和状态
            const messageWithTimestamp = {
                ...message,
                timestamp: Date.now(),
                status: 'pending' // 状态: pending, sending, sent, failed
            };
            
            const request = store.add(messageWithTimestamp);
            
            request.onsuccess = (event) => {
                resolve(event.target.result); // 返回消息ID
            };
            
            request.onerror = (event) => {
                console.error('添加离线消息失败:', event.target.error);
                reject(event.target.error);
            };
            
            transaction.oncomplete = () => {
                db.close();
            };
        });
    } catch (error) {
        console.error('添加离线消息错误:', error);
        return -1;
    }
}

/**
 * 获取所有离线消息
 * @param {string} status - 可选，按状态筛选
 * @returns {Promise<Array>} 离线消息数组
 */
export async function getOfflineMessages(status = null) {
    try {
        const db = await openDatabase();
        return new Promise((resolve, reject) => {
            const transaction = db.transaction([STORE_NAME], 'readonly');
            const store = transaction.objectStore(STORE_NAME);
            
            let request;
            if (status) {
                // 按状态筛选
                const index = store.index('status');
                request = index.getAll(status);
            } else {
                // 获取所有消息
                request = store.getAll();
            }
            
            request.onsuccess = (event) => {
                resolve(event.target.result);
            };
            
            request.onerror = (event) => {
                console.error('获取离线消息失败:', event.target.error);
                reject(event.target.error);
            };
            
            transaction.oncomplete = () => {
                db.close();
            };
        });
    } catch (error) {
        console.error('获取离线消息错误:', error);
        return [];
    }
}

/**
 * 更新离线消息状态
 * @param {number} id - 消息ID
 * @param {string} status - 新状态
 */
export async function updateMessageStatus(id, status) {
    try {
        const db = await openDatabase();
        return new Promise((resolve, reject) => {
            const transaction = db.transaction([STORE_NAME], 'readwrite');
            const store = transaction.objectStore(STORE_NAME);
            
            const getRequest = store.get(id);
            
            getRequest.onsuccess = (event) => {
                const message = event.target.result;
                if (message) {
                    message.status = status;
                    const updateRequest = store.put(message);
                    
                    updateRequest.onsuccess = () => {
                        resolve(true);
                    };
                    
                    updateRequest.onerror = (event) => {
                        console.error('更新消息状态失败:', event.target.error);
                        reject(event.target.error);
                    };
                } else {
                    resolve(false);
                }
            };
            
            getRequest.onerror = (event) => {
                console.error('获取消息失败:', event.target.error);
                reject(event.target.error);
            };
            
            transaction.oncomplete = () => {
                db.close();
            };
        });
    } catch (error) {
        console.error('更新消息状态错误:', error);
        return false;
    }
}

/**
 * 删除离线消息
 * @param {number} id - 消息ID
 */
export async function removeOfflineMessage(id) {
    try {
        const db = await openDatabase();
        return new Promise((resolve, reject) => {
            const transaction = db.transaction([STORE_NAME], 'readwrite');
            const store = transaction.objectStore(STORE_NAME);
            
            const request = store.delete(id);
            
            request.onsuccess = () => {
                resolve(true);
            };
            
            request.onerror = (event) => {
                console.error('删除离线消息失败:', event.target.error);
                reject(event.target.error);
            };
            
            transaction.oncomplete = () => {
                db.close();
            };
        });
    } catch (error) {
        console.error('删除离线消息错误:', error);
        return false;
    }
}

/**
 * 发送所有离线消息
 * @param {Function} sendFunction - 发送消息的函数
 * @param {number} batchSize - 每批发送的消息数量
 * @returns {Promise<Object>} 发送结果
 */
export async function sendAllOfflineMessages(sendFunction, batchSize = 5) {
    try {
        // 获取所有待发送的消息
        const messages = await getOfflineMessages('pending');
        
        if (messages.length === 0) {
            return { success: true, count: 0 };
        }
        
        console.log(`准备发送${messages.length}条离线消息`);
        
        let successCount = 0;
        let failCount = 0;
        
        // 分批发送消息，避免一次发送过多
        for (let i = 0; i < messages.length; i += batchSize) {
            const batch = messages.slice(i, i + batchSize);
            
            // 并行发送当前批次的消息
            const results = await Promise.all(batch.map(async (message) => {
                try {
                    // 更新状态为发送中
                    await updateMessageStatus(message.id, 'sending');
                    
                    // 发送消息
                    const success = await sendFunction(message);
                    
                    if (success) {
                        // 发送成功，删除消息
                        await removeOfflineMessage(message.id);
                        return { success: true };
                    } else {
                        // 发送失败，更新状态
                        await updateMessageStatus(message.id, 'failed');
                        return { success: false };
                    }
                } catch (error) {
                    console.error('发送离线消息错误:', error);
                    await updateMessageStatus(message.id, 'failed');
                    return { success: false, error };
                }
            }));
            
            // 统计当前批次的结果
            for (const result of results) {
                if (result.success) {
                    successCount++;
                } else {
                    failCount++;
                }
            }
        }
        
        return {
            success: failCount === 0,
            count: messages.length,
            successCount,
            failCount
        };
    } catch (error) {
        console.error('发送所有离线消息错误:', error);
        return { success: false, error };
    }
}

/**
 * 清理所有离线消息
 * @returns {Promise<boolean>} 是否成功
 */
export async function clearAllOfflineMessages() {
    try {
        const db = await openDatabase();
        return new Promise((resolve, reject) => {
            const transaction = db.transaction([STORE_NAME], 'readwrite');
            const store = transaction.objectStore(STORE_NAME);
            
            const request = store.clear();
            
            request.onsuccess = () => {
                resolve(true);
            };
            
            request.onerror = (event) => {
                console.error('清理离线消息失败:', event.target.error);
                reject(event.target.error);
            };
            
            transaction.oncomplete = () => {
                db.close();
            };
        });
    } catch (error) {
        console.error('清理离线消息错误:', error);
        return false;
    }
}
