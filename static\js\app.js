/**
 * 聊天应用主入口
 */
import { loadUserFromStorage, checkAndSyncUserState, getCurrentUser } from './modules/user.js';
import { connectWebSocket, updateUserActivity, setPageVisibility } from './modules/websocket.js';
import { handleUnreadCounts, handleOfflineMessages } from './modules/message-status.js';
import { bindEvents } from './modules/events.js';
import { hideLoginForm, updateUIState } from './modules/dom.js';
import * as chatModule from './modules/chat.js';
import { getCurrentChatroom } from './modules/chat.js';
import { initMessageTools, enableMessageTools } from './modules/message-tools.js';
import { initImageHandler, initImagePreview } from './modules/image-handler.js';
import { initVoiceCall, enableVoiceCallButton } from './modules/voice-call.js';
import { handleCardConfirmationMessage } from './modules/card-messages.js';

/**
 * 更新会话时间戳
 */
function updateSessionTimestamp() {
    const user = loadUserFromStorage();
    if (user) {
        // 更新会话时间戳
        localStorage.setItem('session_timestamp', new Date().getTime().toString());
    }
}

/**
 * 初始化应用
 */
function init() {
    // 绑定事件
    bindEvents();

    // 初始化消息工具
    initMessageTools();

    // 初始化图片预览功能
    initImagePreview();

    // 初始化图片处理
    initImageHandler();

    // 初始化语音通话功能
    initVoiceCall();

    // 将enableMessageTools挂载到window对象上，便于dom.js调用
    window.enableMessageTools = enableMessageTools;

    // 检查并同步用户状态，确保用户名称显示正确
    checkAndSyncUserState();

    // 设置定时器，每5分钟更新一次会话时间戳
    setInterval(updateSessionTimestamp, 5 * 60 * 1000);

    // 每分钟检查一次用户状态，确保显示一致性
    setInterval(checkAndSyncUserState, 60 * 1000);

    // 检查本地存储中是否有用户信息
    const user = loadUserFromStorage();
    if (user) {
        // 隐藏登录表单
        hideLoginForm();

        // 设置会话过期时间，每次页面加载时更新
        const now = new Date().getTime();
        localStorage.setItem('session_timestamp', now);

        // 连接WebSocket
        connectWebSocket(user.id, () => {
            // WebSocket连接成功后的回调
            // 延迟一下再检查当前聊天室
            setTimeout(() => {
                const currentChatroom = getCurrentChatroom();
                if (currentChatroom) {
                    // 如果有当前聊天室，启用工具栏
                    enableMessageTools(true);
                }
            }, 1000);
        }, {
            'system': (data) => console.log(`系统消息: ${data.msg}`),
            'chatroom_list': (data) => {
                chatModule.setChatrooms(data.chatrooms);
            },
            'new_message': (data) => chatModule.handleNewMessage(data),
            'message_sent': (data) => chatModule.handleNewMessage(data),
            'message_updated': (data) => chatModule.handleNewMessage(data),
            'history_messages': (data) => chatModule.handleHistoryMessages(data),
            'new_chatroom': (data) => {
                const newChatroom = data.chatroom;
                console.log("收到新聊天室:", newChatroom);
                chatModule.updateChatroom(newChatroom);

                // 如果当前正在查看该聊天室，更新成员面板
                const currentChatroom = chatModule.getCurrentChatroom();
                if (currentChatroom && currentChatroom.id === newChatroom.id) {
                    chatModule.displayCurrentChatInfo(newChatroom.id);
                }
                // 自动选择新创建的聊天室
                const currentUser = getCurrentUser();
                if (!currentChatroom || (newChatroom.is_group && newChatroom.members.includes(currentUser.id))) {
                    console.log("自动切换到新群组:", newChatroom.name);
                    const chatToSelect = chatModule.getAllChatrooms().find(c => c.id === newChatroom.id);
                    if (chatToSelect) {
                        chatModule.selectChatroom(chatToSelect);
                    }
                }
                // 如果是新的私聊，也自动选择
                else if (!newChatroom.is_group && newChatroom.members.includes(currentUser.id)) {
                    console.log("自动切换到新私聊");
                    const chatToSelect = chatModule.getAllChatrooms().find(c => c.id === newChatroom.id);
                    if (chatToSelect) {
                        chatModule.selectChatroom(chatToSelect);
                    }
                }
            },
            'add_to_group_success': (data) => {
                alert(`用户已被添加到群组`);

                // 如果当前聊天室是该群组，刷新群组信息
                const currentChatroom = chatModule.getCurrentChatroom();
                if (currentChatroom && currentChatroom.id === data.group_id) {
                    chatModule.displayCurrentChatInfo(currentChatroom.id);
                }
            },
            'unread_counts': (data) => {
                console.log("收到未读消息计数:", data);
                handleUnreadCounts(data);
            },
            'offline_messages': (data) => {
                console.log("收到离线消息, 聊天室:", data.chatroom_id, "消息数量:", data.messages.length);

                // 显示通知，提醒用户有新消息
                if (data.messages.length > 0) {
                    const lastMsg = data.messages[data.messages.length - 1];
                    const sender = lastMsg.sender_name || lastMsg.sender_id;
                    const content = lastMsg.content.length > 20 ? lastMsg.content.substring(0, 20) + '...' : lastMsg.content;

                    // 如果浏览器支持通知，显示桌面通知
                    if ("Notification" in window && Notification.permission === "granted") {
                        new Notification("新离线消息", {
                            body: `来自 ${sender} 的 ${data.messages.length} 条新消息: ${content}`,
                            icon: "/static/img/logo.png"
                        });
                    }
                }

                // 处理离线消息，但不自动选择聊天室
                handleOfflineMessages(data);
            },
            'card_confirmation': (data) => {
                console.log("收到卡片确认消息:", data);
                // 处理卡片确认消息
                handleCardConfirmationMessage(data);
            }
        }).catch(error => {
            console.error('连接WebSocket失败:', error);
            updateUIState(false);
        });
    }
}

/**
 * 监听用户活动，更新会话时间戳和WebSocket连接状态
 */
function setupActivityListeners() {
    // 监听用户交互事件
    const events = ['mousedown', 'keydown', 'scroll', 'touchstart'];

    events.forEach(eventType => {
        document.addEventListener(eventType, () => {
            // 只在用户登录时更新会话时间戳和用户活动状态
            if (loadUserFromStorage()) {
                // 更新会话时间戳
                localStorage.setItem('session_timestamp', new Date().getTime().toString());

                // 更新WebSocket用户活动状态
                updateUserActivity();
            }
        }, { passive: true }); // passive: true 提高性能
    });

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
        // 获取当前页面可见性状态
        const isVisible = document.visibilityState === 'visible';

        // 更新WebSocket页面可见性状态
        setPageVisibility(isVisible);

        if (isVisible) {
            // 页面变为可见时，检查并同步用户状态
            checkAndSyncUserState();

            // 更新会话时间戳
            if (loadUserFromStorage()) {
                localStorage.setItem('session_timestamp', new Date().getTime().toString());
            }
        }
    });

    // 监听网络状态变化
    window.addEventListener('online', () => {
        console.log('网络已恢复连接');
        // 网络恢复时，检查WebSocket连接状态
        if (loadUserFromStorage()) {
            // 更新用户活动状态，这会触发连接检查
            updateUserActivity();
        }
    });
}

// 在 DOM 加载完成后初始化应用
window.addEventListener('DOMContentLoaded', () => {
    init();
    setupActivityListeners();
});