/**
 * 消息工具模块 - 处理表情、图片上传、快捷回复和卡片消息
 */
import { getCurrentChatroom } from './chat.js';
import { getCurrentUser } from './user.js';
import { sendChatMessage } from './websocket.js';
import { handleImageUpload } from './image-handler.js';
import { cardTypes, getFormTemplate, sendCardMessage, initCardMessagePanel, bindFormEvents, bindCardConfirmationEvents } from './card-messages.js';

// =============== 常量与配置 ===============

// 常用表情列表
const commonEmojis = [
    '😀', '😁', '😂', '🤣', '😃', '😄', '😅', '😆',
    '😉', '😊', '😋', '😎', '😍', '😘', '🥰', '😗',
    '😙', '😚', '🙂', '🤗', '🤩', '🤔', '🤨', '😐',
    '😑', '😶', '🙄', '😏', '😣', '😥', '😮', '🤐',
    '😯', '😪', '😫', '🥱', '😴', '😌', '😛', '😜',
    '😝', '🤤', '😒', '😓', '😔', '😕', '🙃', '🤑',
    '😲', '☹️', '🙁', '😖', '😞', '😟', '😤', '😢',
    '😭', '😦', '😧', '😨', '😩', '🤯', '😬', '😰'
];

// DOM元素
let elements = {
    emojiBtn: null,
    imageBtn: null,
    quickReplyBtn: null,
    cardMessageBtn: null,
    voiceCallBtn: null,
    imageUpload: null,
    emojiPicker: null,
    quickReplyPanel: null,
    cardMessagePanel: null,
    cardEditModal: null,
    cardFormContainer: null,
    sendCardBtn: null,
    cancelCardBtn: null,
    messageInput: null
};

// =============== 公共 API ===============

/**
 * 初始化消息工具
 */
export function initMessageTools() {
    // 获取DOM元素
    elements.emojiBtn = document.getElementById('emoji-btn');
    elements.imageBtn = document.getElementById('image-btn');
    elements.quickReplyBtn = document.getElementById('quick-reply-btn');
    elements.cardMessageBtn = document.getElementById('card-message-btn');
    elements.voiceCallBtn = document.getElementById('voice-call-btn');
    elements.imageUpload = document.getElementById('image-upload');
    elements.emojiPicker = document.getElementById('emoji-picker');
    elements.quickReplyPanel = document.getElementById('quick-reply-panel');
    elements.cardMessagePanel = document.getElementById('card-message-panel');
    elements.cardEditModal = document.getElementById('card-edit-modal');
    elements.cardFormContainer = document.getElementById('card-form-container');
    elements.sendCardBtn = document.getElementById('send-card-btn');
    elements.cancelCardBtn = document.getElementById('cancel-card-btn');
    elements.messageInput = document.getElementById('message-input');

    // 初始化表情选择器
    initEmojiPicker();

    // 初始化卡片消息面板
    initCardMessagePanel(elements.cardMessagePanel);

    // 绑定卡片确认事件
    bindCardConfirmationEvents();

    // 绑定事件
    bindEvents();
}

/**
 * 启用/禁用消息工具
 * @param {boolean} enabled - 是否启用
 */
export function enableMessageTools(enabled) {
    // 检查元素是否存在
    if (!elements.emojiBtn || !elements.imageBtn || !elements.quickReplyBtn || !elements.cardMessageBtn || !elements.voiceCallBtn) {
        console.error('消息工具按钮元素未找到');
        return;
    }

    // 设置按钮状态
    elements.emojiBtn.disabled = !enabled;
    elements.imageBtn.disabled = !enabled;
    elements.quickReplyBtn.disabled = !enabled;
    elements.cardMessageBtn.disabled = !enabled;
    elements.voiceCallBtn.disabled = !enabled;

    // 如果禁用，隐藏面板
    if (!enabled) {
        toggleEmojiPicker(false);
        toggleQuickReplyPanel(false);
        toggleCardMessagePanel(false);
        if (elements.cardEditModal) {
            elements.cardEditModal.style.display = 'none';
        }
    }

    console.log(`消息工具状态已更新: ${enabled ? '启用' : '禁用'}`);

    // 触发事件，通知其他模块消息工具状态变化
    document.dispatchEvent(new CustomEvent('message-tools-state-changed', { detail: { enabled } }));
}

// =============== 事件处理 ===============

/**
 * 初始化表情选择器
 */
function initEmojiPicker() {
    const emojiContainer = elements.emojiPicker.querySelector('.emoji-container');
    emojiContainer.innerHTML = '';

    // 添加表情
    commonEmojis.forEach(emoji => {
        const emojiItem = document.createElement('span');
        emojiItem.className = 'emoji-item';
        emojiItem.textContent = emoji;
        emojiItem.addEventListener('click', () => {
            insertEmoji(emoji);
            toggleEmojiPicker(false);
        });
        emojiContainer.appendChild(emojiItem);
    });
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 表情按钮点击事件
    elements.emojiBtn.addEventListener('click', () => {
        toggleEmojiPicker();
        toggleQuickReplyPanel(false);
        toggleCardMessagePanel(false);
    });

    // 图片按钮点击事件
    elements.imageBtn.addEventListener('click', () => {
        elements.imageUpload.click();
        toggleEmojiPicker(false);
        toggleQuickReplyPanel(false);
        toggleCardMessagePanel(false);
    });

    // 快捷回复按钮点击事件
    elements.quickReplyBtn.addEventListener('click', () => {
        toggleQuickReplyPanel();
        toggleEmojiPicker(false);
        toggleCardMessagePanel(false);
    });

    // 卡片消息按钮点击事件
    elements.cardMessageBtn.addEventListener('click', () => {
        toggleCardMessagePanel();
        toggleEmojiPicker(false);
        toggleQuickReplyPanel(false);
    });

    // 图片上传事件 - 使用图片处理模块
    elements.imageUpload.addEventListener('change', handleImageUpload);

    // 快捷回复项点击事件
    document.querySelectorAll('.quick-reply-item').forEach(item => {
        item.addEventListener('click', () => {
            const text = item.getAttribute('data-text');
            if (text) {
                elements.messageInput.value = text;
                toggleQuickReplyPanel(false);
            }
        });
    });

    // 卡片类型选择事件
    document.addEventListener('click', (e) => {
        if (e.target.closest('.card-type-item')) {
            const item = e.target.closest('.card-type-item');
            const cardType = item.getAttribute('data-card-type');
            showCardEditModal(cardType);
            toggleCardMessagePanel(false);
        }
    });

    // 发送卡片按钮点击事件
    elements.sendCardBtn.addEventListener('click', () => {
        const cardType = elements.cardFormContainer.getAttribute('data-card-type');
        if (sendCardMessage(cardType)) {
            elements.cardEditModal.style.display = 'none';
        }
    });

    // 取消按钮点击事件
    elements.cancelCardBtn.addEventListener('click', () => {
        elements.cardEditModal.style.display = 'none';
    });

    // 关闭模态框按钮点击事件
    elements.cardEditModal.querySelector('.close-modal').addEventListener('click', () => {
        elements.cardEditModal.style.display = 'none';
    });

    // 点击其他区域关闭面板
    document.addEventListener('click', (e) => {
        if (!elements.emojiBtn.contains(e.target) &&
            !elements.emojiPicker.contains(e.target) &&
            !elements.quickReplyBtn.contains(e.target) &&
            !elements.quickReplyPanel.contains(e.target) &&
            !elements.cardMessageBtn.contains(e.target) &&
            !elements.cardMessagePanel.contains(e.target) &&
            !e.target.closest('.card-type-item')) {
            toggleEmojiPicker(false);
            toggleQuickReplyPanel(false);
            toggleCardMessagePanel(false);
        }
    });
}

// =============== 表情、快捷回复和卡片消息面板 ===============

/**
 * 切换表情选择器显示状态
 * @param {boolean} [force] - 强制设置状态
 */
function toggleEmojiPicker(force) {
    const newState = force !== undefined ? force : elements.emojiPicker.style.display !== 'block';
    elements.emojiPicker.style.display = newState ? 'block' : 'none';

    if (newState) {
        // 定位到按钮上方
        const buttonRect = elements.emojiBtn.getBoundingClientRect();
        const toolbarRect = document.querySelector('.message-toolbar').getBoundingClientRect();

        // 设置位置，使面板显示在按钮上方
        elements.emojiPicker.style.left = `${buttonRect.left}px`;
        elements.emojiPicker.style.top = `${toolbarRect.top - elements.emojiPicker.offsetHeight - 10}px`;

        console.log('显示表情选择器');
    }
}

/**
 * 切换快捷回复面板显示状态
 * @param {boolean} [force] - 强制设置状态
 */
function toggleQuickReplyPanel(force) {
    const newState = force !== undefined ? force : elements.quickReplyPanel.style.display !== 'block';
    elements.quickReplyPanel.style.display = newState ? 'block' : 'none';

    if (newState) {
        // 定位到按钮上方
        const buttonRect = elements.quickReplyBtn.getBoundingClientRect();
        const toolbarRect = document.querySelector('.message-toolbar').getBoundingClientRect();

        // 设置位置，使面板显示在按钮上方
        elements.quickReplyPanel.style.left = `${buttonRect.left}px`;
        elements.quickReplyPanel.style.top = `${toolbarRect.top - elements.quickReplyPanel.offsetHeight - 10}px`;

        console.log('显示快捷回复面板');
    }
}

/**
 * 切换卡片消息面板显示状态
 * @param {boolean} [force] - 强制设置状态
 */
function toggleCardMessagePanel(force) {
    const newState = force !== undefined ? force : elements.cardMessagePanel.style.display !== 'block';
    elements.cardMessagePanel.style.display = newState ? 'block' : 'none';

    if (newState) {
        // 定位到按钮上方
        const buttonRect = elements.cardMessageBtn.getBoundingClientRect();
        const toolbarRect = document.querySelector('.message-toolbar').getBoundingClientRect();

        // 设置位置，使面板显示在按钮上方
        elements.cardMessagePanel.style.left = `${buttonRect.left}px`;
        elements.cardMessagePanel.style.top = `${toolbarRect.top - elements.cardMessagePanel.offsetHeight - 10}px`;

        console.log('显示卡片消息面板');
    }
}

/**
 * 显示卡片编辑模态框
 * @param {string} cardType - 卡片类型
 */
function showCardEditModal(cardType) {
    // 设置模态框标题
    document.getElementById('card-edit-title').textContent = `编辑${cardTypes[cardType].name}`;

    // 加载表单模板
    elements.cardFormContainer.innerHTML = getFormTemplate(cardType);

    // 存储当前卡片类型
    elements.cardFormContainer.setAttribute('data-card-type', cardType);

    // 绑定表单事件
    bindFormEvents();

    // 显示模态框
    elements.cardEditModal.style.display = 'block';
}

/**
 * 插入表情到输入框
 * @param {string} emoji - 表情字符
 */
function insertEmoji(emoji) {
    const input = elements.messageInput;
    const startPos = input.selectionStart;
    const endPos = input.selectionEnd;
    const text = input.value;

    input.value = text.substring(0, startPos) + emoji + text.substring(endPos);

    // 设置光标位置
    input.selectionStart = input.selectionEnd = startPos + emoji.length;
    input.focus();
}
