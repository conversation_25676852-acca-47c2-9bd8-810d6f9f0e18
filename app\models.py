from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean, Table, Index
from sqlalchemy.orm import relationship, declarative_base
from datetime import datetime
import json

Base = declarative_base()

# 用户-聊天室关联表
user_chatroom = Table(
    "user_chatroom",
    Base.metadata,
    Column("user_id", String(36), ForeignKey("users.id")),
    Column("chatroom_id", Integer, ForeignKey("chatrooms.id"))
)

class User(Base):
    __tablename__ = "users"

    id = Column(String(36), primary_key=True)
    username = Column(String(50), nullable=False)
    avatar = Column(String(255), nullable=True)

    # 关系
    messages = relationship("Message", back_populates="sender")
    chatrooms = relationship("ChatRoom", secondary=user_chatroom, back_populates="members")

class ChatRoom(Base):
    __tablename__ = "chatrooms"

    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    name = Column(String(100), nullable=True)  # 群聊名称，私聊为空
    is_group = Column(Boolean, default=False)  # 是否为群聊
    created_at = Column(DateTime, default=datetime.now)

    # 关系
    messages = relationship("Message", back_populates="chatroom", order_by="desc(Message.created_at)")
    members = relationship("User", secondary=user_chatroom, back_populates="chatrooms")

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "is_group": self.is_group,
            "created_at": self.created_at.isoformat(),
            "members": [member.id for member in self.members],
            "last_message": self.messages[0].to_dict() if self.messages else None
        }

class Message(Base):
    __tablename__ = "messages"

    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    content = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    is_edited = Column(Boolean, default=False)
    is_html = Column(Boolean, default=False)  # 标记消息是否包含HTML
    message_type = Column(String(50), nullable=True)  # 消息类型，如"text", "image", "file", "call_signal"等
    content_type = Column(String(50), default="text")  # 内容类型，如"text", "image", "file"等

    # 外键
    sender_id = Column(String(36), ForeignKey("users.id"))
    chatroom_id = Column(Integer, ForeignKey("chatrooms.id"))

    # 关系
    sender = relationship("User", back_populates="messages")
    chatroom = relationship("ChatRoom", back_populates="messages")

    def to_dict(self):
        return {
            "id": self.id,
            "content": self.content,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "is_edited": self.is_edited,
            "is_html": self.is_html,  # 添加is_html字段
            "message_type": self.message_type,  # 添加message_type字段
            "content_type": self.content_type,  # 添加content_type字段
            "sender_id": self.sender_id,
            "chatroom_id": self.chatroom_id,
            "sender_name": self.sender.username if self.sender else None
        }


class CardConfirmation(Base):
    """卡片确认记录表"""
    __tablename__ = "card_confirmations"

    id = Column(Integer, primary_key=True, autoincrement=True, nullable=False)
    card_id = Column(String(100), nullable=False, comment="卡片ID")
    card_type = Column(String(50), nullable=False, comment="卡片类型")
    message_id = Column(Integer, ForeignKey("messages.id"), nullable=True, comment="关联的消息ID")
    chatroom_id = Column(Integer, ForeignKey("chatrooms.id"), nullable=False, comment="聊天室ID")
    confirmed_by = Column(String(36), ForeignKey("users.id"), nullable=False, comment="确认用户ID")
    confirmed_at = Column(DateTime, default=datetime.now, nullable=False, comment="确认时间")

    # 关系
    message = relationship("Message", backref="card_confirmations")
    chatroom = relationship("ChatRoom")
    user = relationship("User")

    # 索引
    __table_args__ = (
        Index('idx_card_confirmation_card', 'card_id', 'card_type'),
        Index('idx_card_confirmation_chatroom', 'chatroom_id'),
    )

    def to_dict(self):
        return {
            "id": self.id,
            "card_id": self.card_id,
            "card_type": self.card_type,
            "message_id": self.message_id,
            "chatroom_id": self.chatroom_id,
            "confirmed_by": self.confirmed_by,
            "confirmed_at": self.confirmed_at.isoformat(),
            "user_name": self.user.username if self.user else None
        }