import requests
import json

# 服务器地址
BASE_URL = "http://localhost:8000/api/chat"

def test_create_chat():
    """测试创建聊天消息的API"""
    
    # 准备请求数据
    data = {
        "msg": "你好，这是一条测试消息！",
        "sender": "user1",
        "recipient": "user2"
    }
    
    # 发送POST请求
    response = requests.post(f"{BASE_URL}/create_chat", json=data)
    
    # 打印响应
    print(f"状态码: {response.status_code}")
    print(f"响应内容: {response.json()}")
    
    return response.json()

if __name__ == "__main__":
    print("开始测试聊天API...")
    test_create_chat()
    print("测试完成！") 