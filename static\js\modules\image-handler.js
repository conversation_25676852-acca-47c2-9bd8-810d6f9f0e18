/**
 * 图片处理模块 - 处理图片加载、上传和预览
 */
import { getCurrentChatroom } from './chat.js';
import { getCurrentUser } from './user.js';
import { sendChatMessage } from './websocket.js';

// =============== 常量与配置 ===============

// 图片缓存
const imageCache = {};

// 图片上传配置
const IMAGE_UPLOAD_CONFIG = {
    maxSize: 5 * 1024 * 1024, // 5MB
    minTimeout: 30000,        // 最小超时时间（30秒）
    timeoutPerMB: 5000,       // 每MB增加的超时时间（5秒）
    uploadEndpoint: '/api/upload/upload_base64_image'
};

// =============== 公共 API ===============

/**
 * 初始化图片处理
 */
export function initImageHandler() {
    // 监听DOM变化，为新添加的图片添加加载事件
    observeImageElements();
    
    // 处理已存在的图片
    handleExistingImages();
}

/**
 * 初始化图片预览功能
 * 将图片预览功能挂载到window对象上，便于在HTML中调用
 */
export function initImagePreview() {
    window.showImagePreview = showImagePreview;
}

/**
 * 处理图片上传
 * @param {Event} e - 事件对象
 */
export function handleImageUpload(e) {
    const file = e.target.files[0];
    if (!file) return;

    // 验证图片
    if (!validateImage(file)) {
        return;
    }

    // 读取文件
    const reader = new FileReader();
    reader.onload = (event) => {
        const imageData = event.target.result;
        sendImageMessage(imageData);
    };
    reader.readAsDataURL(file);

    // 清空文件输入，以便可以再次选择同一文件
    e.target.value = '';
}

// =============== 图片监听与处理 ===============

/**
 * 观察DOM变化，处理新添加的图片
 */
function observeImageElements() {
    const observer = new MutationObserver((mutations) => {
        mutations.forEach(mutation => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1) { // 元素节点
                        // 查找新添加的图片
                        const images = node.querySelectorAll('.message-image');
                        images.forEach(img => {
                            if (!img.hasAttribute('data-handled')) {
                                handleImage(img);
                            }
                        });

                        // 如果节点本身是图片
                        if (node.classList && node.classList.contains('message-image') && !node.hasAttribute('data-handled')) {
                            handleImage(node);
                        }
                    }
                });
            }
        });
    });

    // 开始观察
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}

/**
 * 处理已存在的图片
 */
function handleExistingImages() {
    document.querySelectorAll('.message-image').forEach(img => {
        if (!img.hasAttribute('data-handled')) {
            handleImage(img);
        }
    });
}

/**
 * 处理图片元素
 * @param {HTMLImageElement} img - 图片元素
 */
function handleImage(img) {
    // 标记为已处理
    img.setAttribute('data-handled', 'true');

    // 添加loading类
    if (!img.complete) {
        img.classList.add('loading');
    }

    // 添加加载事件
    img.addEventListener('load', () => {
        img.classList.remove('loading');
    });

    // 添加错误处理
    img.addEventListener('error', () => handleImageError(img));

    // 如果图片已经在缓存中或已加载完成
    if (imageCache[img.src] || img.complete) {
        img.classList.remove('loading');
    } else {
        // 缓存图片
        const cacheImg = new Image();
        cacheImg.src = img.src;
        imageCache[img.src] = true;
    }
}

/**
 * 处理图片加载错误
 * @param {HTMLImageElement} img - 图片元素
 */
function handleImageError(img) {
    img.classList.remove('loading');
    img.classList.add('error');

    // 防止无限循环：只有当当前src不是错误图片时才设置错误图片
    if (!img.src.endsWith('/static/img/image-error.png')) {
        img.src = '/static/img/image-error.png';
    } else {
        // 如果错误图片本身也加载失败，则使用内联样式显示错误图标
        applyErrorImageStyles(img);
        console.error('错误图片(image-error.png)加载失败');
    }
}

/**
 * 应用错误图片样式
 * @param {HTMLImageElement} img - 图片元素
 */
function applyErrorImageStyles(img) {
    img.removeAttribute('src');
    // 添加内联样式显示错误图标
    img.style.minWidth = '100px';
    img.style.minHeight = '100px';
    img.style.backgroundColor = '#ffebee';
    img.style.border = '1px solid #ffcdd2';
    img.style.display = 'flex';
    img.style.alignItems = 'center';
    img.style.justifyContent = 'center';
    img.style.position = 'relative';

    // 添加错误图标
    if (!img.querySelector('.error-icon')) {
        const errorIcon = document.createElement('div');
        errorIcon.className = 'error-icon';
        errorIcon.innerHTML = '\u26A0'; // 警告符号
        errorIcon.style.fontSize = '24px';
        errorIcon.style.color = '#d32f2f';
        img.appendChild(errorIcon);
    }
}

// =============== 图片上传功能 ===============

/**
 * 验证图片文件
 * @param {File} file - 图片文件
 * @returns {boolean} 验证结果
 */
function validateImage(file) {
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
        alert('请选择图片文件');
        return false;
    }

    // 检查文件大小
    if (file.size > IMAGE_UPLOAD_CONFIG.maxSize) {
        alert(`图片大小不能超过${IMAGE_UPLOAD_CONFIG.maxSize / (1024 * 1024)}MB`);
        return false;
    }
    
    return true;
}

/**
 * 发送图片消息
 * @param {string} imageData - 图片数据（Base64）
 */
function sendImageMessage(imageData) {
    const currentChatroom = getCurrentChatroom();
    const currentUser = getCurrentUser();

    if (!currentChatroom || !currentUser) return;

    // 创建并显示上传中提示
    const loadingMessage = createLoadingMessage();
    document.getElementById('chat-messages').appendChild(loadingMessage);
    document.getElementById('chat-messages').scrollTop = document.getElementById('chat-messages').scrollHeight;

    // 上传图片到服务器
    uploadImageToServer(imageData, currentUser.id, currentChatroom, loadingMessage);
}

/**
 * 创建加载消息元素
 * @returns {HTMLElement} 加载消息元素
 */
function createLoadingMessage() {
    const loadingMessage = document.createElement('div');
    loadingMessage.className = 'message message-sent';
    loadingMessage.innerHTML = `
        <div class="message-content">
            <div class="loading-indicator">
                <span class="loading-text">正在上传图片...</span>
                <div class="loading-spinner"></div>
            </div>
        </div>
    `;
    return loadingMessage;
}

/**
 * 将图片上传到服务器
 * @param {string} imageData - 图片数据（Base64）
 * @param {string} userId - 用户ID
 * @param {Object} currentChatroom - 当前聊天室
 * @param {HTMLElement} loadingElement - 加载提示元素
 */
async function uploadImageToServer(imageData, userId, currentChatroom, loadingElement) {
    try {
        // 显示上传进度提示
        const loadingText = loadingElement.querySelector('.loading-text');
        loadingText.textContent = '正在上传图片...';

        // 计算大小并设置超时
        const { estimatedSize, timeout } = calculateUploadParams(imageData);
        
        // 如果图片超过10MB，显示警告
        if (estimatedSize > 10) {
            loadingText.textContent = '正在上传大图片，请耐心等待...';
        }

        // 准备表单数据
        const formData = new FormData();
        formData.append('image_data', imageData);
        formData.append('user_id', userId);

        // 创建AbortController用于超时控制
        const { response, aborted } = await uploadWithTimeout(formData, timeout);
        
        if (aborted) {
            throw new Error('上传超时，请尝试上传小一点的图片');
        }
        
        if (!response.ok) {
            throw new Error(`上传失败: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        // 移除加载提示
        if (loadingElement.parentNode) {
            loadingElement.parentNode.removeChild(loadingElement);
        }

        // 发送图片消息
        sendImageChatMessage(result.url, currentChatroom, userId);

    } catch (error) {
        handleUploadError(error, loadingElement);
    }
}

/**
 * 计算上传参数
 * @param {string} imageData - 图片数据
 * @returns {Object} 包含估计大小和超时时间的对象
 */
function calculateUploadParams(imageData) {
    // 计算大小
    const estimatedSize = Math.round((imageData.length * 0.75) / (1024 * 1024) * 100) / 100;
    console.log(`图片大小约为: ${estimatedSize}MB`);

    // 设置请求超时时间，大文件需要更长的超时时间
    const timeout = Math.max(
        IMAGE_UPLOAD_CONFIG.minTimeout, 
        estimatedSize * IMAGE_UPLOAD_CONFIG.timeoutPerMB
    ); 
    
    return { estimatedSize, timeout };
}

/**
 * 使用超时机制上传数据
 * @param {FormData} formData - 表单数据
 * @param {number} timeout - 超时时间
 * @returns {Promise<Object>} 包含响应和是否超时的对象
 */
async function uploadWithTimeout(formData, timeout) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    try {
        // 发送请求
        const response = await fetch(IMAGE_UPLOAD_CONFIG.uploadEndpoint, {
            method: 'POST',
            body: formData,
            signal: controller.signal
        });
        
        // 清除超时定时器
        clearTimeout(timeoutId);
        return { response, aborted: false };
    } catch (error) {
        // 清除超时定时器
        clearTimeout(timeoutId);
        
        if (error.name === 'AbortError') {
            return { response: null, aborted: true };
        }
        throw error;
    }
}

/**
 * 发送图片聊天消息
 * @param {string} imageUrl - 图片URL
 * @param {Object} chatroom - 聊天室
 * @param {string} userId - 用户ID
 */
function sendImageChatMessage(imageUrl, chatroom, userId) {
    // 创建图片消息内容 - 使用OSS URL
    // 添加loading类和加载事件
    const messageContent = `<img src="${imageUrl}" class="message-image message-image-sending loading" alt="图片消息" onclick="window.showImagePreview(this.src)" loading="lazy" decoding="async" onload="this.classList.remove('loading')">`;

    // 预加载图片
    const preloadImg = new Image();
    preloadImg.src = imageUrl;

    // 发送消息
    sendChatMessage(chatroom, messageContent, userId, true);
}

/**
 * 处理上传错误
 * @param {Error} error - 错误对象
 * @param {HTMLElement} loadingElement - 加载提示元素
 */
function handleUploadError(error, loadingElement) {
    console.error('上传图片失败:', error);

    // 更新加载提示为错误信息
    if (loadingElement.parentNode) {
        loadingElement.innerHTML = `
            <div class="message-content">
                <div class="error-message">上传失败: ${error.message}</div>
            </div>
        `;

        // 3秒后移除错误信息
        setTimeout(() => {
            if (loadingElement.parentNode) {
                loadingElement.parentNode.removeChild(loadingElement);
            }
        }, 3000);
    }
}

// =============== 图片预览功能 ===============

/**
 * 图片预览功能
 * @param {string} src - 图片路径
 */
function showImagePreview(src) {
    const modal = document.getElementById('image-preview-modal');
    const previewImage = document.getElementById('preview-image');
    const closeBtn = document.querySelector('.image-preview-close');

    // 设置图片源
    previewImage.src = src;

    // 显示模态框
    showPreviewModal(modal);

    // 初始化缩放功能
    initializeZoom(modal, previewImage);

    // 绑定关闭事件
    bindCloseEvents(modal, previewImage, closeBtn);

    // 添加缩放提示
    console.log('图片预览已打开，使用滚轮可以放大/缩小图片');
}

/**
 * 显示预览模态框
 * @param {HTMLElement} modal - 模态框元素
 */
function showPreviewModal(modal) {
    modal.style.display = 'flex';
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

/**
 * 初始化缩放功能
 * @param {HTMLElement} modal - 模态框元素
 * @param {HTMLElement} previewImage - 预览图片元素
 */
function initializeZoom(modal, previewImage) {
    // 初始缩放比例
    let scale = 1;
    const MIN_SCALE = 0.1;
    const MAX_SCALE = 5;
    const SCALE_STEP = 0.2;

    // 重置图片缩放
    previewImage.style.transform = `scale(${scale})`;

    // 添加滚轮缩放事件
    const handleWheel = (e) => {
        e.preventDefault();

        // 判断滚轮方向
        const delta = e.deltaY < 0 ? SCALE_STEP : -SCALE_STEP;
        scale = Math.max(MIN_SCALE, Math.min(MAX_SCALE, scale + delta));

        // 应用缩放
        previewImage.style.transform = `scale(${scale})`;
    };

    // 绑定滚轮事件
    modal.addEventListener('wheel', handleWheel);
    
    // 将handleWheel函数添加到modal元素，以便后续移除
    modal._handleWheel = handleWheel;
}

/**
 * 绑定关闭事件
 * @param {HTMLElement} modal - 模态框元素
 * @param {HTMLElement} previewImage - 预览图片元素
 * @param {HTMLElement} closeBtn - 关闭按钮
 */
function bindCloseEvents(modal, previewImage, closeBtn) {
    // 创建关闭函数
    const closeModal = () => {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
            // 重置缩放
            previewImage.style.transform = 'scale(1)';
            // 移除滚轮事件
            if (modal._handleWheel) {
                modal.removeEventListener('wheel', modal._handleWheel);
            }
        }, 300);
    };

    // 绑定点击关闭按钮
    closeBtn.onclick = closeModal;
    
    // 点击背景关闭
    modal.onclick = (e) => {
        if (e.target === modal) {
            closeModal();
        }
    };

    // 添加ESC键关闭
    document.addEventListener('keydown', function escHandler(e) {
        if (e.key === 'Escape') {
            closeModal();
            document.removeEventListener('keydown', escHandler);
        }
    });
}
