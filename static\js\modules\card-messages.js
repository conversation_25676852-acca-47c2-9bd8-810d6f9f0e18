/**
 * 卡片消息模块 - 处理各种类型的卡片消息
 */
import { getCurrentChatroom } from './chat.js';
import { getCurrentUser } from './user.js';
import { sendChatMessage } from './websocket.js';

// 卡片确认状态缓存
const cardConfirmationsCache = {
  // 格式: { chatroom_id: { card_id: { confirmed_by: 'user_id', confirmed_at: 'timestamp' } } }
};

// 卡片类型定义
const cardTypes = {
  task: {
    name: '任务详情',
    icon: '📋',
    template: generateTaskCardTemplate,
    getFormData: getTaskFormData
  },
  quote: {
    name: '报价单',
    icon: '💰',
    template: generateQuoteCardTemplate,
    getFormData: getQuoteFormData
  },
  confirmation: {
    name: '细节确认',
    icon: '✅',
    template: generateConfirmationCardTemplate,
    getFormData: getConfirmationFormData
  }
};

// 模板生成函数
function generateTaskCardTemplate(data) {
  return `
    <div class="card task-card">
      <div class="card-header">
        <h3 class="task-title">${data.title}</h3>
        <span class="priority-badge priority-${data.priority.toLowerCase()}">${data.priority}</span>
      </div>
      <div class="card-body">
        <p class="task-description">${data.description}</p>
        <div class="task-details">
          <div class="detail-item">
            <span class="detail-label">截止日期:</span>
            <span class="detail-value">${data.dueDate}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">负责人:</span>
            <span class="detail-value">${data.assignee}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">状态:</span>
            <span class="status-badge status-${data.status.toLowerCase()}">${data.status}</span>
          </div>
        </div>
        ${data.notes ? `<p class="additional-notes">${data.notes}</p>` : ''}
        ${data.link ? `
        <div class="card-actions">
          <a href="${data.link}" target="_blank" class="card-action-btn view-task-btn">查看详情</a>
        </div>
        ` : ''}
      </div>
    </div>
  `;
}

function generateQuoteCardTemplate(data) {
  // 生成报价项目行
  const itemRows = data.items.map(item => `
    <tr>
      <td>${item.name}</td>
      <td>${item.quantity}</td>
      <td>${item.price}</td>
      <td>${item.quantity * item.price}</td>
    </tr>
  `).join('');

  // 生成唯一ID，用于确认按钮
  const quoteId = 'quote_' + Date.now();

  return `
    <div class="card quote-card" data-quote-id="${quoteId}">
      <div class="card-header">
        <h3 class="quote-title">${data.title}</h3>
        <div class="quote-client">客户: ${data.client}</div>
      </div>
      <div class="card-body">
        <table class="quote-items">
          <thead>
            <tr>
              <th>项目</th>
              <th>数量</th>
              <th>单价</th>
              <th>小计</th>
            </tr>
          </thead>
          <tbody>
            ${itemRows}
          </tbody>
          <tfoot>
            <tr>
              <td colspan="3" class="total-label">总计</td>
              <td class="total-value">${data.total}</td>
            </tr>
          </tfoot>
        </table>
        <div class="quote-validity">报价有效期至: ${data.validUntil}</div>
        ${data.notes ? `<p class="quote-notes">${data.notes}</p>` : ''}
        <div class="card-actions">
          <button class="card-action-btn confirm-quote-btn" data-quote-id="${quoteId}">确认报价</button>
          <span class="confirmation-status" style="display: none;"></span>
        </div>
      </div>
    </div>
  `;
}

function generateConfirmationCardTemplate(data) {
  // 生成确认项目列表
  const itemList = data.items.map(item => `
    <li class="confirmation-item ${item.confirmed ? 'confirmed' : 'pending'}">
      <div class="item-header">
        <span class="item-status-icon">${item.confirmed ? '✅' : '⏳'}</span>
        <span class="item-name">${item.name}</span>
      </div>
      <p class="item-description">${item.description}</p>
    </li>
  `).join('');

  // 生成唯一ID，用于确认按钮
  const confirmationId = 'confirmation_' + Date.now();

  return `
    <div class="card confirmation-card" data-confirmation-id="${confirmationId}">
      <div class="card-header">
        <h3 class="confirmation-title">${data.title}</h3>
      </div>
      <div class="card-body">
        <ul class="confirmation-items">
          ${itemList}
        </ul>
        <div class="confirmation-details">
          <div class="detail-item">
            <span class="detail-label">截止日期:</span>
            <span class="detail-value">${data.dueDate}</span>
          </div>
          ${data.confirmer ? `
            <div class="detail-item">
              <span class="detail-label">确认方:</span>
              <span class="detail-value">${data.confirmer}</span>
            </div>
          ` : ''}
        </div>
        ${data.notes ? `<p class="additional-notes">${data.notes}</p>` : ''}
        <div class="card-actions">
          <button class="card-action-btn confirm-details-btn" data-confirmation-id="${confirmationId}">确认细节</button>
          <span class="confirmation-status" style="display: none;"></span>
        </div>
      </div>
    </div>
  `;
}

// 表单数据获取函数
function getTaskFormData() {
  return {
    title: document.getElementById('task-title').value,
    description: document.getElementById('task-description').value,
    dueDate: document.getElementById('task-due-date').value,
    priority: document.getElementById('task-priority').value,
    assignee: document.getElementById('task-assignee').value,
    status: document.getElementById('task-status').value,
    link: document.getElementById('task-link').value,
    notes: document.getElementById('task-notes').value
  };
}

function getQuoteFormData() {
  // 获取报价项目
  const itemRows = document.querySelectorAll('.quote-item-row');
  const items = Array.from(itemRows).map(row => {
    const name = row.querySelector('.item-name').value;
    const quantity = parseInt(row.querySelector('.item-quantity').value);
    const price = parseFloat(row.querySelector('.item-price').value);
    return { name, quantity, price };
  });

  // 计算总金额
  const total = items.reduce((sum, item) => sum + (item.quantity * item.price), 0);

  return {
    title: document.getElementById('quote-title').value,
    client: document.getElementById('quote-client').value,
    validUntil: document.getElementById('quote-valid-until').value,
    items: items,
    total: total.toFixed(2),
    notes: document.getElementById('quote-notes').value
  };
}

function getConfirmationFormData() {
  // 获取确认项目
  const itemRows = document.querySelectorAll('.confirmation-item-row');
  const items = Array.from(itemRows).map(row => {
    const name = row.querySelector('.item-name').value;
    const description = row.querySelector('.item-description').value;
    const confirmed = row.querySelector('.item-confirmed').checked;
    return { name, description, confirmed };
  });

  return {
    title: document.getElementById('confirmation-title').value,
    items: items,
    dueDate: document.getElementById('confirmation-due-date').value,
    confirmer: document.getElementById('confirmation-confirmer').value,
    notes: document.getElementById('confirmation-notes').value
  };
}

// 表单模板生成函数
function getTaskFormTemplate() {
  return `
    <div class="form-group">
      <label for="task-title">任务标题</label>
      <input type="text" id="task-title" required>
    </div>
    <div class="form-group">
      <label for="task-description">任务描述</label>
      <textarea id="task-description" required></textarea>
    </div>
    <div class="form-group">
      <label for="task-due-date">截止日期</label>
      <input type="date" id="task-due-date" required>
    </div>
    <div class="form-group">
      <label for="task-priority">优先级</label>
      <select id="task-priority" required>
        <option value="High">高</option>
        <option value="Medium">中</option>
        <option value="Low">低</option>
      </select>
    </div>
    <div class="form-group">
      <label for="task-assignee">负责人</label>
      <input type="text" id="task-assignee" required>
    </div>
    <div class="form-group">
      <label for="task-status">状态</label>
      <select id="task-status" required>
        <option value="Pending">待处理</option>
        <option value="InProgress">进行中</option>
        <option value="Completed">已完成</option>
      </select>
    </div>
    <div class="form-group">
      <label for="task-link">任务链接</label>
      <input type="url" id="task-link" placeholder="https://example.com/task/123">
    </div>
    <div class="form-group">
      <label for="task-notes">附加说明</label>
      <textarea id="task-notes"></textarea>
    </div>
  `;
}

function getQuoteFormTemplate() {
  return `
    <div class="form-group">
      <label for="quote-title">报价标题</label>
      <input type="text" id="quote-title" required>
    </div>
    <div class="form-group">
      <label for="quote-client">客户名称</label>
      <input type="text" id="quote-client" required>
    </div>
    <div class="form-group">
      <label for="quote-valid-until">报价有效期</label>
      <input type="date" id="quote-valid-until" required>
    </div>
    <div class="form-group">
      <label>报价项目</label>
      <div id="quote-items-container">
        <div class="quote-item-row">
          <input type="text" class="item-name" placeholder="项目名称" required>
          <input type="number" class="item-quantity" placeholder="数量" min="1" value="1" required>
          <input type="number" class="item-price" placeholder="单价" min="0" step="0.01" required>
          <button type="button" class="remove-item-btn">删除</button>
        </div>
      </div>
      <button type="button" id="add-quote-item-btn">添加项目</button>
    </div>
    <div class="form-group">
      <label for="quote-notes">备注</label>
      <textarea id="quote-notes"></textarea>
    </div>
  `;
}

function getConfirmationFormTemplate() {
  return `
    <div class="form-group">
      <label for="confirmation-title">确认标题</label>
      <input type="text" id="confirmation-title" required>
    </div>
    <div class="form-group">
      <label>确认项目</label>
      <div id="confirmation-items-container">
        <div class="confirmation-item-row">
          <input type="text" class="item-name" placeholder="项目名称" required>
          <textarea class="item-description" placeholder="详细描述" required></textarea>
          <label>
            <input type="checkbox" class="item-confirmed"> 已确认
          </label>
          <button type="button" class="remove-item-btn">删除</button>
        </div>
      </div>
      <button type="button" id="add-confirmation-item-btn">添加项目</button>
    </div>
    <div class="form-group">
      <label for="confirmation-due-date">截止日期</label>
      <input type="date" id="confirmation-due-date" required>
    </div>
    <div class="form-group">
      <label for="confirmation-confirmer">确认方</label>
      <input type="text" id="confirmation-confirmer">
    </div>
    <div class="form-group">
      <label for="confirmation-notes">附加说明</label>
      <textarea id="confirmation-notes"></textarea>
    </div>
  `;
}

// 获取表单模板
function getFormTemplate(cardType) {
  switch (cardType) {
    case 'task':
      return getTaskFormTemplate();
    case 'quote':
      return getQuoteFormTemplate();
    case 'confirmation':
      return getConfirmationFormTemplate();
    default:
      return '';
  }
}

// 发送卡片消息
function sendCardMessage(cardType) {
  const cardData = cardTypes[cardType].getFormData();
  const htmlContent = cardTypes[cardType].template(cardData);

  const currentChatroom = getCurrentChatroom();
  const currentUser = getCurrentUser();

  if (currentChatroom && currentUser) {
    sendChatMessage(
      currentChatroom,
      htmlContent,
      currentUser.id,
      true, // isHtml = true
      `card_${cardType}`, // messageType
      'card' // contentType
    );

    return true;
  }

  return false;
}

// 初始化卡片消息面板
function initCardMessagePanel(panelElement) {
  // 清空面板
  panelElement.innerHTML = '';

  // 创建卡片类型容器
  const container = document.createElement('div');
  container.className = 'card-message-container';

  // 添加卡片类型选项
  Object.entries(cardTypes).forEach(([type, config]) => {
    const item = document.createElement('div');
    item.className = 'card-type-item';
    item.setAttribute('data-card-type', type);

    const icon = document.createElement('div');
    icon.className = 'card-icon';
    icon.textContent = config.icon;

    const label = document.createElement('div');
    label.className = 'card-label';
    label.textContent = config.name;

    item.appendChild(icon);
    item.appendChild(label);
    container.appendChild(item);
  });

  panelElement.appendChild(container);
}

// 绑定表单事件
function bindFormEvents() {
  // 添加报价项目按钮
  const addQuoteItemBtn = document.getElementById('add-quote-item-btn');
  if (addQuoteItemBtn) {
    addQuoteItemBtn.addEventListener('click', () => {
      const container = document.getElementById('quote-items-container');
      const newRow = document.createElement('div');
      newRow.className = 'quote-item-row';
      newRow.innerHTML = `
        <input type="text" class="item-name" placeholder="项目名称" required>
        <input type="number" class="item-quantity" placeholder="数量" min="1" value="1" required>
        <input type="number" class="item-price" placeholder="单价" min="0" step="0.01" required>
        <button type="button" class="remove-item-btn">删除</button>
      `;
      container.appendChild(newRow);

      // 为新添加的删除按钮绑定事件
      newRow.querySelector('.remove-item-btn').addEventListener('click', (e) => {
        e.target.closest('.quote-item-row').remove();
      });
    });
  }

  // 添加确认项目按钮
  const addConfirmationItemBtn = document.getElementById('add-confirmation-item-btn');
  if (addConfirmationItemBtn) {
    addConfirmationItemBtn.addEventListener('click', () => {
      const container = document.getElementById('confirmation-items-container');
      const newRow = document.createElement('div');
      newRow.className = 'confirmation-item-row';
      newRow.innerHTML = `
        <input type="text" class="item-name" placeholder="项目名称" required>
        <textarea class="item-description" placeholder="详细描述" required></textarea>
        <label>
          <input type="checkbox" class="item-confirmed"> 已确认
        </label>
        <button type="button" class="remove-item-btn">删除</button>
      `;
      container.appendChild(newRow);

      // 为新添加的删除按钮绑定事件
      newRow.querySelector('.remove-item-btn').addEventListener('click', (e) => {
        e.target.closest('.confirmation-item-row').remove();
      });
    });
  }

  // 删除项目按钮
  document.querySelectorAll('.remove-item-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
      e.target.closest('.quote-item-row, .confirmation-item-row').remove();
    });
  });
}

// 处理卡片确认按钮点击事件
function handleCardConfirmation(event) {
  const target = event.target;

  // 如果按钮已禁用，不执行任何操作
  if (target.disabled) {
    return;
  }

  // 获取当前用户和聊天室信息
  const currentUser = getCurrentUser();
  const currentChatroom = getCurrentChatroom();

  if (!currentUser || !currentChatroom) {
    console.error('无法获取当前用户或聊天室信息');
    return;
  }

  // 检查卡片是否已被确认
  const cardElement = target.closest('.quote-card, .confirmation-card');
  if (cardElement && cardElement.getAttribute('data-confirmed') === 'true') {
    console.log('卡片已被确认，无需重复确认');

    // 禁用按钮
    target.disabled = true;
    target.textContent = '已确认';

    // 显示已确认状态
    const confirmedBy = cardElement.getAttribute('data-confirmed-by');
    const confirmedAt = cardElement.getAttribute('data-confirmed-at');
    if (confirmedBy && confirmedAt) {
      const date = new Date(confirmedAt);
      alert(`该卡片已被 ${confirmedBy} 于 ${date.toLocaleString()} 确认`);
    } else {
      alert('该卡片已被确认');
    }

    return;
  }

  // 处理报价单确认
  if (target.classList.contains('confirm-quote-btn')) {
    const quoteId = target.getAttribute('data-quote-id');
    const quoteCard = document.querySelector(`.quote-card[data-quote-id="${quoteId}"]`);

    if (quoteCard) {
      // 禁用按钮
      target.disabled = true;
      target.textContent = '确认中...';

      // 获取消息ID（如果存在）
      const messageElement = quoteCard.closest('.message');
      const messageId = messageElement ? messageElement.getAttribute('data-message-id') : null;

      // 准备确认数据
      const confirmationData = {
        type: 'quote',
        id: quoteId,
        user_id: currentUser.id,
        chatroom_id: currentChatroom.id,
        timestamp: new Date().toISOString(),
        message_id: messageId
      };

      // 发送AJAX请求
      fetch('/api/confirm-card', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(confirmationData)
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('确认请求失败');
        }
        return response.json();
      })
      .then(data => {
        if (data.data.already_confirmed) {
          // 卡片已被确认，显示确认信息
          const confirmedBy = data.data.confirmed_by;
          const confirmedAt = new Date(data.data.confirmed_at);
          alert(`该卡片已被 ${confirmedBy} 于 ${confirmedAt.toLocaleString()} 确认`);
        }

        // 更新确认状态
        updateConfirmationStatus(
          quoteCard,
          data.data.confirmed_by,
          new Date(data.data.confirmed_at)
        );

        // 不需要额外广播确认消息，服务器已经通过Redis广播了

        console.log(`报价单 ${quoteId} 确认状态:`, data);
      })
      .catch(error => {
        console.error('确认请求出错:', error);
        // 恢复按钮状态，允许重试
        target.disabled = false;
        target.textContent = '确认报价';
        alert('确认失败，请重试');
      });
    }
  }

  // 处理细节确认
  else if (target.classList.contains('confirm-details-btn')) {
    const confirmationId = target.getAttribute('data-confirmation-id');
    const confirmationCard = document.querySelector(`.confirmation-card[data-confirmation-id="${confirmationId}"]`);

    if (confirmationCard) {
      // 禁用按钮
      target.disabled = true;
      target.textContent = '确认中...';

      // 获取消息ID（如果存在）
      const messageElement = confirmationCard.closest('.message');
      const messageId = messageElement ? messageElement.getAttribute('data-message-id') : null;

      // 准备确认数据
      const confirmationData = {
        type: 'confirmation',
        id: confirmationId,
        user_id: currentUser.id,
        chatroom_id: currentChatroom.id,
        timestamp: new Date().toISOString(),
        message_id: messageId
      };

      // 发送AJAX请求
      fetch('/api/confirm-card', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(confirmationData)
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('确认请求失败');
        }
        return response.json();
      })
      .then(data => {
        if (data.data.already_confirmed) {
          // 卡片已被确认，显示确认信息
          const confirmedBy = data.data.confirmed_by;
          const confirmedAt = new Date(data.data.confirmed_at);
          alert(`该卡片已被 ${confirmedBy} 于 ${confirmedAt.toLocaleString()} 确认`);
        }

        // 更新确认状态
        updateConfirmationStatus(
          confirmationCard,
          data.data.confirmed_by,
          new Date(data.data.confirmed_at)
        );

        // 不需要额外广播确认消息，服务器已经通过Redis广播了

        console.log(`细节确认 ${confirmationId} 确认状态:`, data);
      })
      .catch(error => {
        console.error('确认请求出错:', error);
        // 恢复按钮状态，允许重试
        target.disabled = false;
        target.textContent = '确认细节';
        alert('确认失败，请重试');
      });
    }
  }
}

// 更新确认状态
function updateConfirmationStatus(cardElement, userId, confirmedAt = null) {
  // 禁用确认按钮
  const confirmBtn = cardElement.querySelector('.card-action-btn');
  if (confirmBtn) {
    confirmBtn.disabled = true;
    confirmBtn.textContent = '已确认';
  }

  // 显示确认状态
  const statusElement = cardElement.querySelector('.confirmation-status');
  if (statusElement) {
    const date = confirmedAt || new Date();
    const formattedDate = date.toLocaleString();
    statusElement.textContent = `已确认 (${userId} - ${formattedDate})`;
    statusElement.style.display = 'inline-block';
    statusElement.classList.add('confirmed');

    // 添加确认标记，防止重复确认
    cardElement.setAttribute('data-confirmed', 'true');
    cardElement.setAttribute('data-confirmed-by', userId);
    cardElement.setAttribute('data-confirmed-at', date.toISOString());
  }
}

// 注意：不再需要单独广播确认消息，服务器已经通过Redis广播了

// 绑定卡片确认事件
function bindCardConfirmationEvents() {
  // 使用事件委托，绑定到消息容器
  const messagesContainer = document.getElementById('chat-messages');
  if (messagesContainer) {
    messagesContainer.addEventListener('click', (e) => {
      if (e.target.classList.contains('confirm-quote-btn') ||
          e.target.classList.contains('confirm-details-btn')) {
        handleCardConfirmation(e);
      }
    });
  }
}

// 从服务器加载卡片确认状态
async function loadCardConfirmations(chatroomId) {
  try {
    if (!chatroomId) {
      console.error('加载卡片确认状态失败: 缺少聊天室ID');
      return;
    }

    // 清除当前聊天室的缓存
    if (cardConfirmationsCache[chatroomId]) {
      delete cardConfirmationsCache[chatroomId];
    }

    // 初始化聊天室的缓存
    cardConfirmationsCache[chatroomId] = {};

    // 从服务器获取确认状态
    const response = await fetch(`/api/card-confirmations?chatroom_id=${chatroomId}`);
    if (!response.ok) {
      throw new Error('获取卡片确认状态失败');
    }

    const result = await response.json();
    if (result.code !== 200 || !result.data) {
      throw new Error('获取卡片确认状态返回错误');
    }

    // 处理确认状态数据
    result.data.forEach(confirmation => {
      // 缓存确认状态
      if (!cardConfirmationsCache[chatroomId][confirmation.card_id]) {
        cardConfirmationsCache[chatroomId][confirmation.card_id] = {
          confirmed_by: confirmation.confirmed_by,
          confirmed_at: confirmation.confirmed_at,
          card_type: confirmation.card_type
        };
      }
    });

    console.log(`已加载 ${result.data.length} 条卡片确认状态`);

    // 更新当前页面上的卡片状态
    applyCardConfirmations(chatroomId);

    return result.data;
  } catch (error) {
    console.error('加载卡片确认状态错误:', error);
    return [];
  }
}

// 应用卡片确认状态到DOM
function applyCardConfirmations(chatroomId) {
  if (!cardConfirmationsCache[chatroomId]) {
    return;
  }

  // 遍历所有卡片类型
  Object.entries(cardTypes).forEach(([type, _]) => {
    let selector;
    let idAttribute;

    if (type === 'quote') {
      selector = '.quote-card';
      idAttribute = 'data-quote-id';
    } else if (type === 'confirmation') {
      selector = '.confirmation-card';
      idAttribute = 'data-confirmation-id';
    } else {
      return; // 跳过不需要确认的卡片类型
    }

    // 查找所有该类型的卡片
    document.querySelectorAll(selector).forEach(cardElement => {
      const cardId = cardElement.getAttribute(idAttribute);
      if (cardId && cardConfirmationsCache[chatroomId][cardId]) {
        const confirmation = cardConfirmationsCache[chatroomId][cardId];
        updateConfirmationStatus(
          cardElement,
          confirmation.confirmed_by,
          new Date(confirmation.confirmed_at)
        );
      }
    });
  });
}

// 处理接收到的卡片确认消息
export function handleCardConfirmationMessage(data) {
  if (!data || !data.card_id || !data.card_type || !data.user_id) {
    console.error('卡片确认消息数据不完整:', data);
    return;
  }

  // 更新缓存
  const chatroomId = data.chatroom_id;
  if (!chatroomId) {
    console.error('卡片确认消息缺少聊天室ID:', data);
    return;
  }

  console.log(`处理卡片确认消息: 类型=${data.card_type}, ID=${data.card_id}, 用户=${data.user_id}, 聊天室=${chatroomId}`);

  // 初始化聊天室缓存（如果不存在）
  if (!cardConfirmationsCache[chatroomId]) {
    cardConfirmationsCache[chatroomId] = {};
  }

  // 更新缓存
  cardConfirmationsCache[chatroomId][data.card_id] = {
    confirmed_by: data.user_id,
    confirmed_at: data.confirmed_at || data.timestamp || new Date().toISOString(),
    card_type: data.card_type
  };

  // 构建选择器
  let cardSelector;
  if (data.card_type === 'quote') {
    cardSelector = `.quote-card[data-quote-id="${data.card_id}"]`;
  } else if (data.card_type === 'confirmation') {
    cardSelector = `.confirmation-card[data-confirmation-id="${data.card_id}"]`;
  } else {
    console.error('未知的卡片类型:', data.card_type);
    return;
  }

  // 查找卡片元素
  const cardElements = document.querySelectorAll(cardSelector);
  if (cardElements.length === 0) {
    console.log(`未找到卡片元素: ${cardSelector}`);
    return;
  }

  console.log(`找到 ${cardElements.length} 个卡片元素，正在更新状态`);

  // 更新所有匹配的卡片元素
  cardElements.forEach(cardElement => {
    // 检查卡片是否已经被确认
    if (cardElement.getAttribute('data-confirmed') === 'true') {
      console.log('卡片已被确认，跳过更新');
      return;
    }

    // 更新确认状态
    updateConfirmationStatus(
      cardElement,
      data.user_id,
      data.confirmed_at ? new Date(data.confirmed_at) : new Date()
    );

    console.log('卡片确认状态已更新');
  });
}

// 导出公共API
export {
  cardTypes,
  getFormTemplate,
  sendCardMessage,
  initCardMessagePanel,
  bindFormEvents,
  bindCardConfirmationEvents,
  loadCardConfirmations
};
