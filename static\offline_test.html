<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线消息队列测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        button {
            padding: 8px 12px;
            cursor: pointer;
        }
        #status {
            margin-top: 20px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
            min-height: 100px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log {
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        .info {
            color: blue;
        }
    </style>
</head>
<body>
    <h1>离线消息队列测试</h1>
    
    <div class="section">
        <h2>IndexedDB离线消息队列</h2>
        <div class="button-group">
            <button id="addMessage">添加测试消息</button>
            <button id="getMessages">获取所有消息</button>
            <button id="clearMessages">清空所有消息</button>
        </div>
    </div>
    
    <div class="section">
        <h2>WebSocket连接测试</h2>
        <div class="button-group">
            <button id="connect">连接WebSocket</button>
            <button id="disconnect">断开连接</button>
            <button id="sendMessage">发送测试消息</button>
        </div>
    </div>
    
    <div class="section">
        <h2>离线消息发送测试</h2>
        <div class="button-group">
            <button id="sendOffline">发送离线消息</button>
            <button id="simulateOffline">模拟离线</button>
            <button id="simulateOnline">模拟在线</button>
        </div>
    </div>
    
    <div id="status">
        <div class="log info">准备就绪，请选择操作...</div>
    </div>
    
    <script type="module">
        import { addOfflineMessage, getOfflineMessages, updateMessageStatus, removeOfflineMessage, sendAllOfflineMessages, clearAllOfflineMessages } from './js/modules/offline-queue.js';
        import { connectWebSocket, sendMessage, getSocket } from './js/modules/websocket.js';
        
        // 状态变量
        let isOnline = true;
        let userId = 'test_user_' + Math.floor(Math.random() * 1000);
        
        // DOM元素
        const statusEl = document.getElementById('status');
        
        // 添加日志
        function log(message, type = 'info') {
            const logEl = document.createElement('div');
            logEl.className = `log ${type}`;
            logEl.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            statusEl.prepend(logEl);
        }
        
        // IndexedDB测试
        document.getElementById('addMessage').addEventListener('click', async () => {
            try {
                const message = {
                    type: 'private_message',
                    msg: '这是一条测试离线消息 ' + new Date().toLocaleTimeString(),
                    sender: userId,
                    recipient: 'test_recipient',
                    timestamp: Date.now()
                };
                
                const id = await addOfflineMessage(message);
                log(`成功添加离线消息，ID: ${id}`, 'success');
            } catch (error) {
                log(`添加离线消息失败: ${error.message}`, 'error');
            }
        });
        
        document.getElementById('getMessages').addEventListener('click', async () => {
            try {
                const messages = await getOfflineMessages();
                log(`获取到${messages.length}条离线消息`, 'success');
                messages.forEach((msg, index) => {
                    log(`消息${index+1}: ${JSON.stringify(msg)}`, 'info');
                });
            } catch (error) {
                log(`获取离线消息失败: ${error.message}`, 'error');
            }
        });
        
        document.getElementById('clearMessages').addEventListener('click', async () => {
            try {
                await clearAllOfflineMessages();
                log('成功清空所有离线消息', 'success');
            } catch (error) {
                log(`清空离线消息失败: ${error.message}`, 'error');
            }
        });
        
        // WebSocket测试
        document.getElementById('connect').addEventListener('click', async () => {
            try {
                await connectWebSocket(userId, () => {
                    log(`WebSocket连接成功，用户ID: ${userId}`, 'success');
                    isOnline = true;
                }, {
                    'system': (data) => log(`系统消息: ${data.msg}`, 'info'),
                    'new_message': (data) => log(`收到新消息: ${JSON.stringify(data.message)}`, 'info')
                });
            } catch (error) {
                log(`WebSocket连接失败: ${error.message}`, 'error');
            }
        });
        
        document.getElementById('disconnect').addEventListener('click', () => {
            const socket = getSocket();
            if (socket) {
                socket.close();
                log('WebSocket连接已断开', 'info');
                isOnline = false;
            } else {
                log('WebSocket未连接', 'error');
            }
        });
        
        document.getElementById('sendMessage').addEventListener('click', () => {
            const message = {
                type: 'private_message',
                msg: '这是一条测试消息 ' + new Date().toLocaleTimeString(),
                sender: userId,
                recipient: 'test_recipient',
                timestamp: Date.now()
            };
            
            const success = sendMessage(message);
            if (success) {
                log('消息发送成功', 'success');
            } else {
                log('消息发送失败，可能已保存到离线队列', 'error');
            }
        });
        
        // 离线消息测试
        document.getElementById('sendOffline').addEventListener('click', async () => {
            try {
                const result = await sendAllOfflineMessages((message) => {
                    if (!isOnline) {
                        log('模拟离线状态，消息发送失败', 'error');
                        return false;
                    }
                    
                    log(`发送离线消息: ${JSON.stringify(message)}`, 'info');
                    return sendMessage(message);
                });
                
                log(`离线消息发送结果: 总数=${result.count}, 成功=${result.successCount}, 失败=${result.failCount}`, 'success');
            } catch (error) {
                log(`发送离线消息失败: ${error.message}`, 'error');
            }
        });
        
        document.getElementById('simulateOffline').addEventListener('click', () => {
            isOnline = false;
            log('已模拟离线状态', 'info');
        });
        
        document.getElementById('simulateOnline').addEventListener('click', () => {
            isOnline = true;
            log('已模拟在线状态', 'info');
        });
        
        // 初始化
        log(`测试用户ID: ${userId}`, 'info');
    </script>
</body>
</html>
