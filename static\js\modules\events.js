/**
 * 事件处理模块
 */
import { elements } from './dom.js';
import { login, getCurrentUser as getUserFromUserModule } from './user.js';
import { connectWebSocket, sendMessage as wsSendMessage } from './websocket.js';
import { resetUnreadCount } from './message-status.js';
import {
    sendMessage,
    loadMoreMessages,
    selectChatroom,
    setChatrooms,
    updateChatroom,
    handleHistoryMessages,
    handleNewMessage,
    getCurrentChatroom,
    startChat,
    displayCurrentChatInfo as displayChatInfo,
    getAllChatrooms as getChatrooms,
    setSelectedMembers as setMembers
} from './chat.js';
import { createNewGroup, searchGroupUsers } from './group.js';

// =============== 常量与配置 ===============

// 存储WebSocket消息处理器
const messageHandlers = {
    'system': handleSystemMessage,
    'chatroom_list': handleChatroomList,
    'new_message': handleNewMessage,
    'message_sent': handleNewMessage,
    'message_updated': handleMessageUpdated,
    'history_messages': handleHistoryMessages,
    'new_chatroom': handleNewChatroom,
    'add_to_group_success': handleAddToGroupSuccess
};

// =============== 公共函数 ===============

/**
 * 绑定事件
 */
export function bindEvents() {
    // 登录功能
    bindLoginEvents();

    // 消息发送功能
    bindMessageEvents();

    // 聊天功能
    bindChatEvents();

    // 群组功能
    bindGroupEvents();

    // 模态框
    bindModalEvents();
}

/**
 * 获取当前用户
 * @returns {Object} 当前用户
 */
function getCurrentUser() {
    return getUserFromUserModule();
}

// =============== 事件绑定分组 ===============

/**
 * 绑定登录相关事件
 */
function bindLoginEvents() {
    elements.loginBtn.addEventListener('click', handleLogin);
}

/**
 * 绑定消息相关事件
 */
function bindMessageEvents() {
    // 发送消息按钮点击
    elements.sendBtn.addEventListener('click', sendMessage);

    // 消息输入框按Enter发送
    elements.messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // 加载更多消息按钮点击
    elements.loadMoreBtn.addEventListener('click', loadMoreMessages);

    // 聊天区域滚动到顶部时加载更多消息
    elements.chatMessages.addEventListener('scroll', handleChatScroll);
}

/**
 * 绑定聊天相关事件
 */
function bindChatEvents() {
    // 新建聊天按钮点击
    elements.newChatBtn.addEventListener('click', () => {
        openModal(elements.newChatModal, '新建聊天', 'new_chat');
    });

    // 用户搜索按钮点击
    elements.searchBtn.addEventListener('click', handleUserSearch);
}

/**
 * 绑定群组相关事件
 */
function bindGroupEvents() {
    // 创建群组按钮点击
    elements.newGroupBtn.addEventListener('click', openNewGroupModal);

    // 群组用户搜索按钮点击
    elements.groupSearchBtn.addEventListener('click', () => {
        searchGroupUsers(elements.groupUserSearch.value.trim());
    });

    // 创建群组按钮点击
    elements.createGroupBtn.addEventListener('click', createNewGroup);

    // 添加用户按钮点击
    elements.addUserBtn.addEventListener('click', handleAddUserButtonClick);

    // 右侧面板中的添加成员按钮
    if (elements.panelAddMemberBtn) {
        elements.panelAddMemberBtn.addEventListener('click', handleAddUserButtonClick);
    }
}

/**
 * 绑定模态框相关事件
 */
function bindModalEvents() {
    // 关闭模态框
    elements.closeModals.forEach(closeBtn => {
        closeBtn.addEventListener('click', () => {
            closeModals();
        });
    });

    // 点击模态框外部关闭
    window.addEventListener('click', (e) => {
        if (e.target === elements.newChatModal || e.target === elements.newGroupModal) {
            closeModals();
        }
    });
}

// =============== 事件处理函数 ===============

/**
 * 处理聊天区域滚动，实现到顶加载更多消息
 */
function handleChatScroll() {
    // 如果滚动到距离顶部5px以内，且有更多消息可加载（按钮可见）
    if (elements.chatMessages.scrollTop <= 5 &&
        elements.loadMoreBtn.style.display === 'block' &&
        !document.getElementById('loading-indicator')) {
        loadMoreMessages();
    }
}

/**
 * 处理登录
 */
async function handleLogin() {
    const user = login();
    if (user) {
        try {
            // 连接WebSocket
            await connectWebSocket(user.id, null, messageHandlers);
        } catch (error) {
            console.error('连接WebSocket失败:', error);
            alert('连接服务器失败，请刷新页面重试');
        }
    }
}

/**
 * 处理添加用户按钮点击
 */
function handleAddUserButtonClick() {
    const currentChatroom = getCurrentChatroom();
    if (currentChatroom && currentChatroom.is_group) {
        openModal(elements.newChatModal, '添加用户到群组', 'add_to_group');
    }
}

/**
 * 处理用户搜索
 */
async function handleUserSearch() {
    const keyword = elements.userSearch.value.trim();
    if (!keyword) return;

    try {
        const response = await fetch(`/api/chat/users/search?keyword=${encodeURIComponent(keyword)}`);
        const data = await response.json();

        if (data.code === 200) {
            renderUserSearchResults(data.data);
        }
    } catch (error) {
        console.error('搜索用户失败:', error);
    }
}

/**
 * 渲染用户搜索结果
 * @param {Array} users - 用户列表
 */
function renderUserSearchResults(users) {
    // 确定当前操作的模式
    const isAddingToGroup = elements.newChatModal.dataset.mode === 'add_to_group';
    const currentChatroom = getCurrentChatroom();

    // 渲染用户搜索结果
    elements.userSearchResults.innerHTML = '';

    if (users.length === 0) {
        elements.userSearchResults.innerHTML = '<div class="no-results">没有找到用户</div>';
        return;
    }

    // 根据不同模式过滤和处理用户
    let filteredUsers = users;

    if (isAddingToGroup && currentChatroom && currentChatroom.is_group) {
        // 添加用户到群组模式：过滤掉已在群组中的用户
        filteredUsers = users.filter(user => !currentChatroom.members.includes(user.id));
    }

    if (filteredUsers.length === 0) {
        elements.userSearchResults.innerHTML = '<div class="no-results">没有可添加的用户</div>';
        return;
    }

    filteredUsers.forEach(user => {
        const userItem = createUserItem(user, isAddingToGroup, currentChatroom);
        elements.userSearchResults.appendChild(userItem);
    });
}

/**
 * 创建用户项
 * @param {Object} user - 用户信息
 * @param {boolean} isAddingToGroup - 是否添加到群组
 * @param {Object} currentChatroom - 当前聊天室
 * @returns {HTMLElement} 用户项元素
 */
function createUserItem(user, isAddingToGroup, currentChatroom) {
    const userItem = document.createElement('div');
    userItem.className = 'user-item';
    userItem.innerHTML = `
        <img src="${user.avatar || '/static/img/default-avatar.png'}" alt="头像">
        <div>${user.username}</div>
    `;

    userItem.addEventListener('click', () => {
        if (isAddingToGroup && currentChatroom) {
            // 添加用户到群组
            addUserToGroup(user.id, currentChatroom.id);
        } else {
            // 开始私聊
            elements.newChatModal.style.display = 'none';
            startChat(user.id);
        }
    });

    return userItem;
}

// =============== 模态框操作函数 ===============

/**
 * 打开模态框
 * @param {HTMLElement} modal - 模态框元素
 * @param {string} title - 标题
 * @param {string} mode - 模式
 */
function openModal(modal, title, mode) {
    modal.dataset.mode = mode;
    document.querySelector('#new-chat-modal .modal-header h3').textContent = title;
    elements.userSearch.value = '';
    elements.userSearchResults.innerHTML = '';
    modal.style.display = 'block';
}

/**
 * 打开新建群组模态框
 */
function openNewGroupModal() {
    // 重置用户搜索输入框
    elements.groupUserSearch.value = '';
    // 清空搜索结果
    elements.groupUserSearchResults.innerHTML = '';
    // 清空群组名称输入框
    elements.groupName.value = '';
    // 重置已选成员列表
    setMembers([]);
    // 显示创建群组模态框
    elements.newGroupModal.style.display = 'block';
}

/**
 * 关闭所有模态框
 */
function closeModals() {
    elements.newChatModal.style.display = 'none';
    elements.newChatModal.dataset.mode = ''; // 重置模式
    elements.newGroupModal.style.display = 'none';
}

// =============== 群组操作函数 ===============

/**
 * 添加用户到群组
 * @param {string} userId - 用户ID
 * @param {string} groupId - 群组ID
 */
function addUserToGroup(userId, groupId) {
    wsSendMessage({
        type: 'add_to_group',
        user_id: userId,
        group_id: groupId,
        sender: getCurrentUser().id
    });

    // 关闭模态框
    elements.newChatModal.style.display = 'none';

    alert(`已添加用户到群组`);
}

// =============== WebSocket消息处理函数 ===============

/**
 * 处理系统消息
 * @param {Object} data - 消息数据
 */
function handleSystemMessage(data) {
    console.log(`系统消息: ${data.msg}`);
}

/**
 * 处理聊天室列表
 * @param {Object} data - 消息数据
 */
function handleChatroomList(data) {
    console.log("收到聊天室列表, 数量:", data.chatrooms.length);
    setChatrooms(data.chatrooms);
}

/**
 * 处理消息更新
 * @param {Object} data - 消息数据
 */
function handleMessageUpdated(data) {
    // 调用chat模块处理消息更新逻辑
    handleNewMessage(data);
}

/**
 * 处理新聊天室
 * @param {Object} data - 消息数据
 */
function handleNewChatroom(data) {
    const newChatroom = data.chatroom;
    console.log("收到新聊天室:", newChatroom);

    // 更新聊天室列表
    updateChatroom(newChatroom);

    // 如果当前正在查看该聊天室，更新成员面板
    const currentChatroom = getCurrentChatroom();
    if (currentChatroom && currentChatroom.id === newChatroom.id) {
        displayChatInfo(newChatroom.id);
    }

    // 自动选择新创建的聊天室
    const currentUser = getCurrentUser();
    if (shouldSelectNewChatroom(currentChatroom, newChatroom, currentUser)) {
        selectNewChatroom(newChatroom.id);
    }
}

/**
 * 判断是否应该选择新聊天室
 * @param {Object} currentChatroom - 当前聊天室
 * @param {Object} newChatroom - 新聊天室
 * @param {Object} currentUser - 当前用户
 * @returns {boolean} 是否应该选择
 */
function shouldSelectNewChatroom(currentChatroom, newChatroom, currentUser) {
    // 没有当前聊天室，选择新聊天室
    if (!currentChatroom) return true;

    // 新的群组且当前用户是成员
    if (newChatroom.is_group && newChatroom.members.includes(currentUser.id)) return true;

    // 新的私聊且当前用户是成员
    if (!newChatroom.is_group && newChatroom.members.includes(currentUser.id)) return true;

    return false;
}

/**
 * 选择新聊天室
 * @param {string} chatroomId - 聊天室ID
 */
function selectNewChatroom(chatroomId) {
    const chatToSelect = getChatrooms().find(c => c.id === chatroomId);
    if (chatToSelect) {
        selectChatroom(chatToSelect);
    }
}

/**
 * 处理添加用户到群组成功
 * @param {Object} data - 消息数据
 */
function handleAddToGroupSuccess(data) {
    alert(`用户已被添加到群组`);

    // 如果当前聊天室是该群组，刷新群组信息
    const currentChatroom = getCurrentChatroom();
    if (currentChatroom && currentChatroom.id === data.group_id) {
        displayChatInfo(currentChatroom.id);
    }
}