/**
 * 语音通话模块 - 使用腾讯云TRTC实现语音通话功能
 */
import { getCurrentUser } from './user.js';
import { getCurrentChatroom, getAllChatrooms } from './chat.js';
import { sendChatMessage } from './websocket.js';

// TRTC实例
let trtc = null;
// 当前通话状态
let callState = {
    isInCall: false,
    isCaller: false,
    remoteUserIds: [], // 改为数组，支持多人通话
    isGroupCall: false, // 是否为群组通话
    roomId: null,
    chatroomId: null, // 存储通话相关的聊天室ID
    waitingTimerInterval: null,
    participants: {}, // 存储参与者信息 {userId: {username: '', avatar: ''}}
    activeSpeakers: [], // 当前正在说话的用户
    isMuted: false, // 麦克风是否静音
    callTimeoutTimer: null // 通话超时计时器
};

// DOM元素
let elements = {
    callBtn: null,
    callModal: null,
    callStatus: null,
    acceptBtn: null,
    rejectBtn: null,
    hangupBtn: null,
    incomingCallModal: null,
    callerName: null
};

// 事件监听器
let socketMessageListener = null;

/**
 * 初始化语音通话功能
 */
export function initVoiceCall() {
    // 加载TRTC SDK
    loadTRTCScript()
        .then(() => {
            console.log('TRTC SDK 加载成功');
            // 检查浏览器兼容性
            if (window.TRTC && window.TRTC.isSupported()) {
                console.log('当前浏览器支持TRTC');
                // 初始化UI元素
                initCallUI();
                // 绑定事件
                bindEvents();
                // 添加WebSocket消息监听
                addSocketMessageListener();
            } else {
                console.error('当前浏览器不支持TRTC，无法使用语音通话功能');
                // 禁用通话按钮
                const callBtn = document.getElementById('voice-call-btn');
                if (callBtn) {
                    callBtn.disabled = true;
                    callBtn.title = '当前浏览器不支持语音通话';
                }
            }
        })
        .catch(error => {
            console.error('加载TRTC SDK失败:', error);
        });
}

/**
 * 加载TRTC SDK脚本
 * @returns {Promise} 加载完成的Promise
 */
function loadTRTCScript() {
    return new Promise((resolve, reject) => {
        // 检查是否已加载
        if (window.TRTC) {
            resolve();
            return;
        }

        const script = document.createElement('script');
        script.src = '/static/js/trtc.js';
        script.async = true;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

/**
 * 初始化通话UI元素
 */
function initCallUI() {
    // 获取已有元素
    elements.callBtn = document.getElementById('voice-call-btn');

    // 创建通话模态框
    createCallModal();
    createIncomingCallModal();

    // 获取创建的元素
    elements.callModal = document.getElementById('call-modal');
    elements.callStatus = document.getElementById('call-status');
    elements.hangupBtn = document.getElementById('hangup-btn');
    elements.incomingCallModal = document.getElementById('incoming-call-modal');
    elements.callerName = document.getElementById('caller-name');
    elements.acceptBtn = document.getElementById('accept-call-btn');
    elements.rejectBtn = document.getElementById('reject-call-btn');
}

/**
 * 创建通话模态框
 */
function createCallModal() {
    const modal = document.createElement('div');
    modal.id = 'call-modal';
    modal.className = 'call-modal';
    modal.style.display = 'none';

    modal.innerHTML = `
        <div class="call-modal-content">
            <div class="call-header">
                <h3 id="call-title">语音通话</h3>
            </div>
            <div class="call-body">
                <div class="call-avatar-container">
                    <div class="call-avatar single-mode">
                        <img id="remote-avatar" src="/static/img/default-avatar.png" alt="对方头像">
                    </div>
                    <div id="participants-container" class="participants-container group-mode" style="display: none;">
                        <!-- 参与者头像将动态添加到这里 -->
                    </div>
                </div>
                <div id="call-status" class="call-status">正在通话中...</div>
                <div class="call-timer" id="call-timer">00:00</div>
                <div class="call-controls">
                    <button id="mic-toggle-btn" class="mic-btn">
                        <span class="mic-icon">🔊</span>
                        <span>静音</span>
                    </button>
                    <button id="hangup-btn" class="hangup-btn">
                        <span class="hangup-icon">&#128222;</span>
                        <span>挂断</span>
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .call-avatar-container {
            width: 100%;
            display: flex;
            justify-content: center;
            margin-bottom: 15px;
        }
        .call-avatar.single-mode {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            overflow: hidden;
            margin: 0 auto 15px;
        }
        .participants-container.group-mode {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
            max-width: 300px;
        }
        .participant {
            position: relative;
            width: 60px;
            height: 60px;
            margin: 5px;
            text-align: center;
        }
        .participant img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #ccc;
        }
        .participant.speaking img {
            border-color: #4CAF50;
            box-shadow: 0 0 8px #4CAF50;
        }
        .participant .name {
            font-size: 10px;
            margin-top: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 60px;
        }
        .call-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        .mic-btn, .hangup-btn {
            padding: 8px 15px;
            border-radius: 20px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.2s;
        }
        .mic-btn {
            background-color: #f0f0f0;
            color: #333;
        }
        .mic-btn.muted {
            background-color: #ff6b6b;
            color: white;
        }
        .mic-btn.active {
            background-color: #4CAF50;
            color: white;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(76, 175, 80, 0); }
            100% { box-shadow: 0 0 0 0 rgba(76, 175, 80, 0); }
        }
        .mic-btn .mic-icon, .hangup-btn .hangup-icon {
            margin-right: 5px;
            font-size: 16px;
        }
    `;
    document.head.appendChild(style);
}

/**
 * 创建来电模态框
 */
function createIncomingCallModal() {
    const modal = document.createElement('div');
    modal.id = 'incoming-call-modal';
    modal.className = 'call-modal incoming-call-modal';
    modal.style.display = 'none';

    modal.innerHTML = `
        <div class="call-modal-content">
            <div class="call-header">
                <h3 id="incoming-call-title">来电</h3>
            </div>
            <div class="call-body">
                <div class="call-avatar">
                    <img id="caller-avatar" src="/static/img/default-avatar.png" alt="来电者头像">
                </div>
                <div class="call-status"><span id="caller-name">有人</span>正在呼叫你...</div>
                <div class="call-controls">
                    <button id="accept-call-btn" class="accept-btn">
                        <span class="accept-icon">&#128222;</span>
                        <span>接听</span>
                    </button>
                    <button id="reject-call-btn" class="reject-btn">
                        <span class="reject-icon">&#128222;</span>
                        <span>拒绝</span>
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

/**
 * 绑定事件
 */
function bindEvents() {
    // 通话按钮点击事件
    if (elements.callBtn) {
        elements.callBtn.addEventListener('click', initiateCall);
    }

    // 挂断按钮点击事件
    elements.hangupBtn.addEventListener('click', endCall);

    // 接听按钮点击事件
    elements.acceptBtn.addEventListener('click', acceptCall);

    // 拒绝按钮点击事件
    elements.rejectBtn.addEventListener('click', rejectCall);

    // 麦克风控制按钮点击事件
    const micToggleBtn = document.getElementById('mic-toggle-btn');
    if (micToggleBtn) {
        micToggleBtn.addEventListener('click', toggleMicrophone);
    }
}

/**
 * 添加WebSocket消息监听
 */
function addSocketMessageListener() {
    // 移除之前的监听器
    if (socketMessageListener) {
        document.removeEventListener('ws-message', socketMessageListener);
    }

    // 添加新的监听器
    socketMessageListener = (event) => {
        const data = event.detail;

        // 处理通话相关消息
        switch (data.type) {
            case 'call_request':
                handleIncomingCall(data);
                break;
            case 'call_accepted':
                handleCallAccepted(data);
                break;
            case 'call_rejected':
                handleCallRejected(data);
                break;
            case 'call_ended':
                handleCallEnded(data);
                break;
            case 'call_room_update':
                handleRoomUpdate(data);
                break;
            case 'call_user_left':
                handleUserLeft(data);
                break;
            case 'call_signal_error':
                console.error('通话信号错误:', data.error);
                alert(`通话信号发送错误: ${data.error}`);
                break;
        }
    };

    document.addEventListener('ws-message', socketMessageListener);
}

/**
 * 处理用户离开群组通话
 * @param {Object} data - 用户离开数据
 */
function handleUserLeft(data) {
    if (data.is_group_call && callState.isInCall && callState.isGroupCall) {
        // 显示用户离开的消息
        const currentUser = getCurrentUser();
        if (currentUser && callState.chatroomId) {
            // 尝试从参与者列表中获取用户名
            let userName = '有人';

            if (data.sender && callState.participants[data.sender]) {
                userName = callState.participants[data.sender].username || data.sender;
                // 从参与者列表中移除
                delete callState.participants[data.sender];
                // 更新UI
                updateParticipantsUI();
            } else {
                userName = data.user_name || data.sender || '有人';
            }

            // 发送消息到聊天室
            sendChatMessageToChatroom(currentUser.id, `${userName} 退出了群组通话`, callState.chatroomId);

            // 检查是否只剩下自己一个人
            checkGroupCallStatus();
        }
    }
}

/**
 * 处理房间ID更新
 * @param {Object} data - 房间更新数据
 */
function handleRoomUpdate(data) {
    if (data.numeric_room_id && data.is_group_call) {
        // 更新数字房间ID
        callState.numericRoomId = data.numeric_room_id;
        console.log('收到房间ID更新:', data.numeric_room_id);
    }
}

/**
 * 发起通话
 */
function initiateCall() {
    const currentChatroom = getCurrentChatroom();
    const currentUser = getCurrentUser();

    if (!currentChatroom || !currentUser) {
        console.error('无法发起通话：未选择聊天室或未登录');
        return;
    }

    // 如果已经在通话中，提示用户
    if (callState.isInCall) {
        alert('您已经在通话中');
        return;
    }

    // 清空参与者列表
    callState.remoteUserIds = [];
    callState.participants = {};

    // 判断是私聊还是群聊
    callState.isGroupCall = currentChatroom.is_group;

    if (callState.isGroupCall) {
        // 群组通话，添加所有成员
        callState.remoteUserIds = currentChatroom.members.filter(id => id !== currentUser.id);
        if (callState.remoteUserIds.length === 0) {
            console.error('群聊中没有其他成员');
            return;
        }

        // 保存群组名称
        callState.groupName = currentChatroom.name || '';
    } else {
        // 私聊通话，只添加对方
        const remoteUserId = currentChatroom.members.find(id => id !== currentUser.id);
        if (!remoteUserId) {
            console.error('无法获取对方用户ID');
            return;
        }
        callState.remoteUserIds.push(remoteUserId);
    }

    // 更新通话状态
    callState.isInCall = true;
    callState.isCaller = true;

    // 生成房间ID
    if (callState.isGroupCall) {
        // 群组通话使用聊天室ID作为房间ID
        callState.roomId = `group_${currentChatroom.id}_${Date.now()}`;
    } else {
        // 私聊通话使用原来的方式生成房间ID
        callState.roomId = generateRoomId(currentUser.id, callState.remoteUserIds[0]);
    }

    // 发送通话请求
    if (callState.isGroupCall) {
        // 群组通话，向所有成员发送请求
        sendGroupCallRequest(callState.remoteUserIds, callState.roomId, currentChatroom.id);
    } else {
        // 私聊通话，只向对方发送请求
        sendCallRequest(callState.remoteUserIds[0], callState.roomId);
    }

    // 显示通话界面
    const statusText = callState.isGroupCall ?
        `正在等待群成员加入 (0/${callState.remoteUserIds.length})...` :
        '正在等待对方接听...';
    showCallUI(statusText);

    // 启动等待计时器
    startWaitingTimer();

    // 播放铃声，发起方也需要听到铃声
    playRingtone();

    // 启动通话超时计时器（60秒后如果无人接听则自动结束）
    startCallTimeoutTimer(60000);

    // 创建TRTC实例并进入房间
    createTRTCInstance();
}

/**
 * 处理来电
 * @param {Object} data - 通话请求数据
 */
function handleIncomingCall(data) {
    const currentUser = getCurrentUser();
    if (!currentUser) return;

    // 如果已经在通话中，自动拒绝
    if (callState.isInCall) {
        sendCallRejected(data.sender, data.roomId, 'busy');
        return;
    }

    // 清空参与者列表
    callState.remoteUserIds = [];
    callState.participants = {};

    // 更新通话状态
    callState.remoteUserIds.push(data.sender);
    callState.roomId = data.roomId;

    // 判断是否为群组通话
    callState.isGroupCall = data.is_group_call || false;

    // 如果是群组通话，保存群聊室ID和群组名称
    if (callState.isGroupCall && data.chatroom_id) {
        callState.chatroomId = data.chatroom_id;
        callState.groupName = data.group_name || '';
    }

    // 显示来电界面
    const callerName = data.sender_name || '未知用户';
    let callType;

    if (callState.isGroupCall) {
        // 如果有群组名称，则显示"xxx群组语音通话"
        const groupName = callState.groupName || data.group_name || '';
        callType = groupName ? `${groupName} 群组语音通话` : '群组语音通话';
    } else {
        callType = '语音通话';
    }

    showIncomingCallUI(callerName, callType);

    // 播放来电铃声
    playRingtone();
}

/**
 * 接受通话
 */
function acceptCall() {
    // 停止铃声
    stopRingtone();

    // 清除通话超时计时器
    if (callState.callTimeoutTimer) {
        clearTimeout(callState.callTimeoutTimer);
        callState.callTimeoutTimer = null;
    }

    // 隐藏来电界面
    elements.incomingCallModal.style.display = 'none';

    // 更新通话状态
    callState.isInCall = true;
    callState.isCaller = false;

    // 发送接受通话消息
    if (callState.isGroupCall) {
        // 群组通话，发送给发起者
        sendCallAccepted(callState.remoteUserIds[0], callState.roomId, true);

        // 在群聊中发送通知消息
        const currentUser = getCurrentUser();
        if (currentUser && callState.chatroomId) {
            sendChatMessageToChatroom(
                currentUser.id,
                `加入了群组语音通话`,
                callState.chatroomId
            );
        }
    } else {
        // 私聊通话，发送给对方
        sendCallAccepted(callState.remoteUserIds[0], callState.roomId);
    }

    // 显示通话界面
    const statusText = callState.isGroupCall ?
        `正在加入群组通话...` :
        '正在连接...';
    showCallUI(statusText);

    // 启动等待计时器
    startWaitingTimer();

    // 创建TRTC实例并进入房间
    createTRTCInstance();
}

/**
 * 拒绝通话
 */
function rejectCall() {
    // 停止铃声
    stopRingtone();

    // 隐藏来电界面
    elements.incomingCallModal.style.display = 'none';

    // 发送拒绝通话消息
    const currentUser = getCurrentUser();
    if (currentUser && callState.remoteUserIds.length > 0) {
        // 发送拒绝信号给发起者
        sendCallSignal('call_rejected', callState.remoteUserIds[0], callState.roomId, {
            reason: 'rejected',
            is_group_call: callState.isGroupCall,
            sender_name: currentUser.username || currentUser.id
        });

        // 在自己的聊天界面显示消息
        const message = callState.isGroupCall ? '已拒绝群组通话' : '已拒绝通话';
        if(callState.isGroupCall){
            sendChatMessageToChatroom(currentUser.id, message, callState.chatroomId);
        }else{
            sendChatMessageAsUser(currentUser.id, message);
        }
    }

    // 重置通话状态
    resetCallState();
}

/**
 * 处理通话被接受
 * @param {Object} data - 接受通话数据
 */
function handleCallAccepted(data) {
    // 停止铃声
    stopRingtone();

    // 清除通话超时计时器
    if (callState.callTimeoutTimer) {
        clearTimeout(callState.callTimeoutTimer);
        callState.callTimeoutTimer = null;
    }

    // 判断是否为群组通话
    const isGroupCall = data.is_group_call || callState.isGroupCall;

    // 添加到参与者列表
    if (data.sender && !callState.participants[data.sender]) {
        callState.participants[data.sender] = {
            username: data.sender_name || data.sender,
            avatar: '/static/img/default-avatar.png'
        };
    }

    // 更新UI
    if (isGroupCall) {
        updateParticipantsUI();
        // 更新状态文本，显示当前参与人数
        const participantCount = Object.keys(callState.participants).length + 1; // +1 包括自己
        elements.callStatus.textContent = `通话已接通 - 参与者: ${participantCount}`;
    } else {
        elements.callStatus.textContent = '通话已接通';
    }

    // 开始计时
    startCallTimer();

    // 如果是群组通话，检查通话状态
    if (isGroupCall) {
        checkGroupCallStatus();
    }
}

/**
 * 处理通话被拒绝
 * @param {Object} data - 拒绝通话数据
 */
function handleCallRejected(data) {
    let message;

    // 判断是否为群组通话
    const isGroupCall = data.is_group_call || callState.isGroupCall;

    if (data.reason === 'busy') {
        message = '对方正忙，无法接听';
        // 在聊天框中显示消息，使用拒绝者的账户发送
        sendChatMessageAsUser(data.sender, message);
    }
    // 如果是群组通话，且有人拒绝加入，不结束整个通话
    if (isGroupCall) {
        // 从远程用户列表中移除拒绝者
        callState.remoteUserIds = callState.remoteUserIds.filter(id => id !== data.sender);

        // 检查是否还有其他人参与通话
        if (callState.remoteUserIds.length === 0 && callState.isCaller) {
            // 如果没有人参与，结束通话
            endCall(false);
        } else if (Object.keys(callState.participants).length === 0 && callState.isCaller) {
            // 如果没有人加入，但还有其他人可能加入，继续等待
            // 不结束通话
        }
    } else {
        // 如果是私聊通话，直接结束
        endCall(false);
    }
}

/**
 * 处理通话结束
 * @param {Object} data - 结束通话数据
 */
function handleCallEnded(data) {
    // 停止铃声 - 无论是否在通话中，都停止铃声
    stopRingtone();

    // 判断是否为群组通话
    const isGroupCall = data.is_group_call || callState.isGroupCall;

    if (isGroupCall) {
        // 如果是群组通话，只有当发起者结束通话时，才结束整个通话
        // 或者当自己是发起者时，收到结束信号也结束通话
        if (data.sender === callState.remoteUserIds[0] || callState.isCaller) {
            // 发起者结束了通话，或者自己是发起者
            endCall(false);
        } else {
            // 如果是普通参与者结束了通话，从参与者列表中移除
            if (callState.participants[data.sender]) {
                delete callState.participants[data.sender];
                updateParticipantsUI();

                // 检查是否只剩下自己一个人
                checkGroupCallStatus();
            }
        }
    } else {
        // 如果是私聊通话，直接结束
        endCall(false);
    }
}

/**
 * 获取通话时长的格式化字符串
 * @returns {string} 格式化的通话时长，如 "01:23"
 */
function getCallDuration() {
    const timerElement = document.getElementById('call-timer');
    if (timerElement) {
        return timerElement.textContent || '00:00';
    }
    return '00:00';
}

/**
 * 以指定用户身份发送聊天消息
 * @param {string} userId - 发送者用户ID
 * @param {string} message - 要发送的消息
 */
function sendChatMessageAsUser(userId, message) {
    const currentUser = getCurrentUser();
    const currentChatroom = getCurrentChatroom();

    if (!currentUser || !currentChatroom || !userId) {
        return;
    }

    // 如果是当前用户发送的消息，直接使用正常的发送消息函数
    if (userId === currentUser.id) {
        sendChatMessage(
            currentChatroom,
            message,
            userId,
            false,  // 非HTML内容
            'call_message'  // 标记为通话相关消息
        );
    } else {
        // 如果是以其他用户身份发送，使用特殊消息类型
        sendChatMessage(
            currentChatroom,
            message,
            userId,
            false,
            'call_message',
            true  // 标记为模拟发送
        );
    }
}

/**
 * 向指定聊天室发送消息
 * @param {string} userId - 发送者用户ID
 * @param {string} message - 要发送的消息
 * @param {string} chatroomId - 聊天室ID
 */
function sendChatMessageToChatroom(userId, message, chatroomId) {
    const currentUser = getCurrentUser();
    if (!currentUser || !userId || !chatroomId) {
        return;
    }

    // 获取所有聊天室列表
    const allChatrooms = getAllChatrooms();
    if (!allChatrooms || allChatrooms.length === 0) {
        return;
    }

    // 查找指定ID的聊天室
    const targetChatroom = allChatrooms.find(chatroom => chatroom.id === chatroomId);
    if (!targetChatroom) {
        console.error('找不到指定的聊天室:', chatroomId);
        return;
    }

    // 发送消息到指定聊天室
    if (userId === currentUser.id) {
        sendChatMessage(
            targetChatroom,
            message,
            userId,
            false,  // 非HTML内容
            'call_message'  // 标记为通话相关消息
        );
    } else {
        // 如果是以其他用户身份发送，使用特殊消息类型
        sendChatMessage(
            targetChatroom,
            message,
            userId,
            false,
            'call_message',
            true  // 标记为模拟发送
        );
    }
}

/**
 * 发送群组通话请求
 * @param {Array<string>} recipientIds - 接收者ID数组
 * @param {string} roomId - 房间ID
 * @param {string} chatroomId - 聊天室ID
 * @returns {boolean} 是否成功发送
 */
function sendGroupCallRequest(recipientIds, roomId, chatroomId) {
    const currentUser = getCurrentUser();
    if (!currentUser) {
        console.error(`无法发送群组通话请求: 用户未登录`);
        return false;
    }

    // 存储群聊ID，以便后续消息发送
    callState.chatroomId = chatroomId;

    // 获取群组名称
    const groupName = getGroupName(chatroomId);
    callState.groupName = groupName;

    // 向每个成员发送通话请求
    let allSuccess = true;
    for (const recipientId of recipientIds) {
        // 构建群组通话请求消息
        const message = {
            type: 'call_request',
            sender: currentUser.id,
            sender_name: currentUser.username || currentUser.id,
            recipient: recipientId,
            roomId: roomId,
            timestamp: Date.now(),
            is_group_call: true,
            chatroom_id: chatroomId,
            group_name: groupName
        };

        // 使用现有的WebSocket发送
        const success = sendChatMessage(
            { is_group: false, members: [currentUser.id, recipientId] },
            JSON.stringify(message),
            currentUser.id,
            false,
            'call_signal'
        );

        if (!success) {
            allSuccess = false;
        }
    }

    // 在群聊中发送通知消息，指定发送到当前群聊
    const notificationMessage = groupName
        ? `在 ${groupName} 发起了群组语音通话，等待成员加入...`
        : `发起了群组语音通话，等待成员加入...`;

    sendChatMessageToChatroom(
        currentUser.id,
        notificationMessage,
        chatroomId
    );

    return allSuccess;
}

/**
 * 获取群组名称
 * @param {string} chatroomId - 聊天室ID
 * @returns {string} 群组名称，如果找不到则返回空字符串
 */
function getGroupName(chatroomId) {
    // 获取所有聊天室
    const allChatrooms = getAllChatrooms();
    if (!allChatrooms || allChatrooms.length === 0) {
        return '';
    }

    // 查找指定ID的聊天室
    const chatroom = allChatrooms.find(room => room.id === chatroomId);
    if (!chatroom) {
        return '';
    }

    // 返回群组名称
    return chatroom.name || '';
}

/**
 * 结束通话
 * @param {boolean} [sendEndSignal=true] - 是否发送结束信号
 * @param {boolean} [isUserAction=true] - 是否用户主动结束通话
 */
function endCall(sendEndSignal = true, isUserAction = true) {
    // 如果不在通话中且不在来电界面，直接返回
    if (!callState.isInCall && elements.incomingCallModal.style.display !== 'flex') {
        return;
    }

    // 停止铃声 - 无论是否在通话中，都停止铃声
    stopRingtone();

    const currentUser = getCurrentUser();
    if (!currentUser) {
        // 如果没有当前用户，直接清理状态
        cleanupCall();
        return;
    }

    // 判断是否已经接通通话（通过检查通话状态文本）
    const callStatusText = elements.callStatus.textContent;
    // 获取通话时长
    const callDuration = getCallDuration();
    let message;

    // 如果是群组通话且是用户主动挂断
    if (callState.isGroupCall && isUserAction && sendEndSignal) {
        // 检查是否有其他人加入通话
        const participantCount = Object.keys(callState.participants).length;

        // 如果没有其他人加入通话，或者发起者是唯一一个人，则结束整个通话
        if (participantCount === 0 && callState.isCaller) {
            // 没有人加入，发起者结束整个通话
            message = `取消群组通话，等待时长: ${callDuration}`;

            // 向所有参与者发送结束信号
            if (callState.remoteUserIds.length > 0) {
                sendGroupCallEnded(callState.remoteUserIds, callState.roomId);
            }

            // 在相关群聊界面显示消息
            if (callState.chatroomId) {
                sendChatMessageToChatroom(currentUser.id, message, callState.chatroomId);
            } else {
                sendChatMessageAsUser(currentUser.id, message);
            }
        }
        // 否则，用户只是退出群组通话，而不是结束整个通话
        else {
            if (callStatusText.includes('通话已接通') || callStatusText.includes('参与者')) {
                message = `您已退出群组通话，通话时长: ${callDuration}`;
            } else {
                message = `您已取消加入群组通话，等待时长: ${callDuration}`;
            }

            // 在相关群聊界面显示消息
            if (callState.chatroomId) {
                sendChatMessageToChatroom(currentUser.id, message, callState.chatroomId);
            } else {
                sendChatMessageAsUser(currentUser.id, message);
            }
        }
    }
    // 如果是群组通话但不是用户主动挂断（如系统自动结束）
    else if (callState.isGroupCall && !isUserAction) {
        // 不需要发送任何消息，因为已经在其他地方发送了
    }
    // 如果是群组通话且需要结束整个通话（发起者结束整个通话）
    else if (callState.isGroupCall && sendEndSignal) {
        // 群组通话消息
        if (callStatusText.includes('通话已接通') || callStatusText.includes('参与者')) {
            message = `群组通话已结束，通话时长: ${callDuration}`;
        } else {
            message = `取消群组通话，等待时长: ${callDuration}`;
        }

        // 向所有参与者发送结束信号
        if (callState.remoteUserIds.length > 0) {
            sendGroupCallEnded(callState.remoteUserIds, callState.roomId);
        }

        // 在相关群聊界面显示消息
        if (callState.chatroomId) {
            sendChatMessageToChatroom(currentUser.id, message, callState.chatroomId);
        } else {
            sendChatMessageAsUser(currentUser.id, message);
        }
    }
    // 如果是私聊通话
    else if (!callState.isGroupCall && sendEndSignal) {
        // 私聊通话消息
        if (callStatusText === '通话已接通') {
            message = `通话已结束，通话时长: ${callDuration}`;
        } else {
            message = `取消通话，等待时长: ${callDuration}`;
        }

        // 发送结束信号给对方
        if (callState.remoteUserIds.length > 0) {
            sendCallEnded(callState.remoteUserIds[0], callState.roomId);
        }

        // 在自己的聊天界面显示消息
        sendChatMessageAsUser(currentUser.id, message);
    }

    // 清理通话资源
    cleanupCall();
}

/**
 * 清理通话资源
 */
function cleanupCall() {
    // 停止所有计时器
    stopAllTimers();

    // 清除通话超时计时器
    if (callState.callTimeoutTimer) {
        clearTimeout(callState.callTimeoutTimer);
        callState.callTimeoutTimer = null;
    }

    // 退出TRTC房间
    exitTRTCRoom();

    // 隐藏通话界面
    elements.callModal.style.display = 'none';
    elements.incomingCallModal.style.display = 'none';

    // 重置通话状态
    resetCallState();
}

/**
 * 检查群组通话状态，如果只有一个人则自动结束
 */
function checkGroupCallStatus() {
    if (!callState.isInCall || !callState.isGroupCall) return;

    // 计算参与者数量（包括自己）
    const participantCount = Object.keys(callState.participants).length + 1;

    // 如果只有自己一个人，自动结束通话
    if (participantCount <= 1) {
        const currentUser = getCurrentUser();
        if (currentUser) {
            if (callState.chatroomId) {
                sendChatMessageToChatroom(currentUser.id, '只剩下您一个人，群组通话已自动结束', callState.chatroomId);
            } else {
                sendChatMessageAsUser(currentUser.id, '只剩下您一个人，群组通话已自动结束');
            }
        }
        endCall(false, false);
    }
}

/**
 * 创建TRTC实例并进入房间
 */
async function createTRTCInstance() {
    try {
        // 获取当前用户
        const currentUser = getCurrentUser();
        if (!currentUser) {
            throw new Error('未登录');
        }

        // 创建TRTC实例
        trtc = window.TRTC.create();

        // 监听事件
        setupTRTCEvents();

        // 获取TRTC配置
        const config = await getTRTCConfig();
        const userSig = await getTRTCUserSig(currentUser.id);

        // 准备房间参数
        let roomParams;
        const numericRoomId = callState.isGroupCall
            ? generateNumericRoomId()
            : parseRoomId(callState.roomId);

        roomParams = {
            roomId: callState.numericRoomId || numericRoomId,
            scene: 'rtc',
            sdkAppId: config.sdkAppId,
            userId: currentUser.id,
            userSig: userSig
        };

        // 如果是群组通话且是发起者，需要通知其他参与者房间ID
        if (callState.isGroupCall && callState.isCaller) {
            callState.numericRoomId = numericRoomId;
            notifyRoomUpdate(numericRoomId);
        }

        // 进入房间
        await trtc.enterRoom(roomParams);
        console.log('进入TRTC房间成功');

        // 开启麦克风
        await trtc.startLocalAudio();
        console.log('开启麦克风成功');

        // 开启音量检测，每500毫秒触发一次音量事件
        trtc.enableAudioVolumeEvaluation(500);
        console.log('开启音量检测成功');

    } catch (error) {
        console.error('创建TRTC实例失败:', error);
        handleCallError(error);
    }
}

/**
 * 获取TRTC配置
 * @returns {Promise<Object>} TRTC配置
 */
async function getTRTCConfig() {
    // 实际应用中应该从配置文件或服务器获取
    return {
        sdkAppId: 1600082053 // 在生产环境中应从服务器获取
    };
}

/**
 * 生成数字房间ID
 * @returns {number} 数字房间ID
 */
function generateNumericRoomId() {
    // 生成一个基于时间戳的数字ID，确保在有效范围内
    return Math.floor(Date.now() % 4294967294) + 1; // 确保在 1 到 4294967294 之间
}

/**
 * 解析房间ID为数字
 * @param {string|number} roomId - 房间ID
 * @returns {number} 数字房间ID
 */
function parseRoomId(roomId) {
    try {
        const numericId = parseInt(roomId);
        if (!isNaN(numericId) && numericId > 0 && numericId < 4294967295) {
            return numericId;
        }
    } catch (e) {
        // 解析失败，使用随机ID
    }
    // 如果无效或解析失败，生成一个有效的数字ID
    return Math.floor(Math.random() * 4294967294) + 1;
}

/**
 * 通知其他参与者房间ID更新
 * @param {number} numericRoomId - 数字房间ID
 */
function notifyRoomUpdate(numericRoomId) {
    for (const userId of callState.remoteUserIds) {
        sendCallSignal('call_room_update', userId, callState.roomId, {
            numeric_room_id: numericRoomId,
            is_group_call: true
        });
    }
}

/**
 * 处理通话错误
 * @param {Error} error - 错误对象
 */
function handleCallError(error) {
    // 使用当前用户身份发送错误消息
    const currentUser = getCurrentUser();
    if (currentUser) {
        // 根据错误类型提供更友好的错误消息
        let errorMessage = '无法建立通话';

        if (error.message.includes('UserSig') || error.message.includes('通话凭证')) {
            errorMessage = '通话凭证获取失败，请稍后再试';
        } else if (error.message.includes('麦克风')) {
            errorMessage = '无法访问麦克风，请检查权限设置';
        } else if (error.message.includes('网络')) {
            errorMessage = '网络连接不稳定，无法建立通话';
        } else {
            errorMessage = `通话错误: ${error.message}`;
        }

        sendChatMessageAsUser(currentUser.id, errorMessage);
    }
    endCall();
}

/**
 * 设置TRTC事件监听
 */
function setupTRTCEvents() {
    if (!trtc) return;

    // 监听远端用户进入房间
    trtc.on(window.TRTC.EVENT.REMOTE_USER_ENTER, (event) => {
        console.log('远端用户进入房间:', event.userId);

        // 添加到参与者列表
        if (!callState.participants[event.userId]) {
            callState.participants[event.userId] = {
                username: event.userId, // 这里可以使用用户ID作为默认名称
                avatar: '/static/img/default-avatar.png'
            };
        }

        // 更新UI
        if (callState.isGroupCall) {
            updateParticipantsUI();
            // 更新状态文本，显示当前参与人数
            const participantCount = Object.keys(callState.participants).length + 1; // +1 包括自己
            elements.callStatus.textContent = `通话已接通 - 参与者: ${participantCount}`;
        } else {
            elements.callStatus.textContent = '通话已接通';
        }

        startCallTimer();

        // 如果是群组通话，检查通话状态
        if (callState.isGroupCall) {
            checkGroupCallStatus();
        }
    });

    // 监听远端用户离开房间
    trtc.on(window.TRTC.EVENT.REMOTE_USER_EXIT, (event) => {
        console.log('远端用户离开房间:', event.userId);

        // 从参与者列表中移除
        if (callState.participants[event.userId]) {
            // 获取用户名称，用于显示通知（可用于日志或通知）
            const displayName = callState.participants[event.userId].username || event.userId;
            console.log(`用户 ${displayName} 离开了通话`);

            // 从参与者列表中移除
            delete callState.participants[event.userId];

            // 从正在说话的用户列表中移除
            const speakerIndex = callState.activeSpeakers.indexOf(event.userId);
            if (speakerIndex !== -1) {
                callState.activeSpeakers.splice(speakerIndex, 1);
            }

            // 更新UI
            if (callState.isGroupCall) {
                updateParticipantsUI();

                // 更新状态文本
                const participantCount = Object.keys(callState.participants).length + 1; // +1 包括自己
                if (participantCount > 1) {
                    elements.callStatus.textContent = `通话已接通 - 参与者: ${participantCount}`;
                } else {
                    // 如果只剩下自己一个人，显示通知并结束通话
                    const currentUser = getCurrentUser();
                    if (currentUser) {
                        sendChatMessageAsUser(currentUser.id, '所有参与者已离开，群组通话已自动结束');
                    }
                    endCall(false, false);
                }
            } else {
                // 私聊通话，如果对方离开，直接结束通话
                endCall(false, false);
            }
        }
    });

    // 监听远端音频可用
    trtc.on(window.TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, (event) => {
        console.log('远端音频可用:', event.userId, event.available);

        // 注意：这里不再使用音频可用事件来判断是否正在说话
        // 只是记录音频流是否可用
        if (!event.available) {
            // 如果音频流不可用，确保从活跃发言者列表中移除
            manageSpeaker(event.userId, false);
        }
    });

    // 监听音量大小事件
    trtc.on(window.TRTC.EVENT.AUDIO_VOLUME, (event) => {
        // event.result 是一个数组，包含所有用户的音量信息
        // 每个元素包含 userId 和 volume 属性
        // volume 范围是 0-100

        if (!event.result || !Array.isArray(event.result)) return;

        // 处理每个用户的音量
        event.result.forEach(item => {
            // 设置音量阈值，大于此值认为用户正在说话
            const VOLUME_THRESHOLD = 10;
            const isSpeaking = item.volume > VOLUME_THRESHOLD;

            // 使用活跃发言者管理函数更新状态
            manageSpeaker(item.userId, isSpeaking);

            // 如果是自己，也更新UI
            const currentUser = getCurrentUser();
            if (currentUser && item.userId === currentUser.id) {
                updateLocalSpeakerUI(isSpeaking);
            }
        });
    });

    // 监听错误
    trtc.on(window.TRTC.EVENT.ERROR, (error) => {
        console.error('TRTC错误:', error);
        // 使用当前用户身份发送错误消息
        const currentUser = getCurrentUser();
        if (currentUser) {
            sendChatMessageAsUser(currentUser.id, `通话错误: ${error.message}`);
        }
        endCall();
    });
}

/**
 * 退出TRTC房间
 * @param {number} [retryCount=0] - 重试次数
 * @returns {Promise<boolean>} 是否成功退出
 */
async function exitTRTCRoom(retryCount = 0) {
    if (!trtc) return true; // 如果没有TRTC实例，视为成功

    try {
        // 停止本地音频
        try {
            await trtc.stopLocalAudio();
        } catch (audioError) {
            console.warn('停止本地音频失败，继续退出房间:', audioError);
            // 即使停止音频失败，也继续尝试退出房间
        }

        // 退出房间
        await trtc.exitRoom();

        // 移除所有事件监听器
        removeAllTRTCEventListeners();

        // 销毁实例
        trtc.destroy();
        trtc = null;

        console.log('已退出TRTC房间');
        return true;
    } catch (error) {
        console.error('退出TRTC房间失败:', error);

        // 重试逻辑
        if (retryCount < 2) { // 最多重试2次
            console.log(`尝试重新退出TRTC房间 (${retryCount + 1}/2)...`);
            // 短暂延迟后重试
            await new Promise(resolve => setTimeout(resolve, 500));
            return exitTRTCRoom(retryCount + 1);
        } else {
            // 重试失败后，强制清理
            console.warn('退出TRTC房间重试失败，强制清理资源');
            if (trtc) {
                try {
                    trtc.destroy();
                } catch (e) {
                    console.error('销毁TRTC实例失败:', e);
                }
                trtc = null;
            }
            return false;
        }
    }
}

/**
 * 移除所有TRTC事件监听器
 */
function removeAllTRTCEventListeners() {
    if (!trtc) return;

    try {
        // 移除所有已知的事件监听器
        const events = [
            window.TRTC.EVENT.REMOTE_USER_ENTER,
            window.TRTC.EVENT.REMOTE_USER_EXIT,
            window.TRTC.EVENT.REMOTE_AUDIO_AVAILABLE,
            window.TRTC.EVENT.AUDIO_VOLUME,  // 添加音量事件
            window.TRTC.EVENT.ERROR
        ];

        for (const event of events) {
            trtc.off(event);
        }

        // 关闭音量检测
        try {
            trtc.enableAudioVolumeEvaluation(0); // 设置为0表示关闭
        } catch (e) {
            console.warn('关闭音量检测失败:', e);
        }
    } catch (error) {
        console.warn('移除TRTC事件监听器失败:', error);
    }
}

/**
 * 显示通话界面
 * @param {string} statusText - 状态文本
 */
function showCallUI(statusText) {
    elements.callStatus.textContent = statusText;
    elements.callModal.style.display = 'flex';

    // 设置通话标题
    const callTitle = document.getElementById('call-title');
    if (callTitle) {
        if (callState.isGroupCall) {
            // 如果有群组名称，则显示"xxx群组语音通话"
            const groupName = callState.groupName || '';
            callTitle.textContent = groupName ? `${groupName} - 群组语音通话` : '群组语音通话';
        } else {
            callTitle.textContent = '语音通话';
        }
    }

    // 切换显示模式
    const singleModeAvatar = document.querySelector('.call-avatar.single-mode');
    const groupModeContainer = document.getElementById('participants-container');

    if (callState.isGroupCall) {
        // 群组通话模式
        if (singleModeAvatar) singleModeAvatar.style.display = 'none';
        if (groupModeContainer) groupModeContainer.style.display = 'flex';

        // 清空并重新添加参与者
        updateParticipantsUI();
    } else {
        // 私聊通话模式
        if (singleModeAvatar) singleModeAvatar.style.display = 'block';
        if (groupModeContainer) groupModeContainer.style.display = 'none';

        // 设置远程用户头像
        const remoteAvatar = document.getElementById('remote-avatar');
        if (remoteAvatar && callState.remoteUserIds.length > 0) {
            // 这里可以根据用户ID获取头像
            remoteAvatar.src = '/static/img/default-avatar.png';
        }
    }
}

/**
 * 更新参与者UI
 */
function updateParticipantsUI() {
    const container = document.getElementById('participants-container');
    if (!container) return;

    // 清空容器
    container.innerHTML = '';

    // 添加当前用户
    const currentUser = getCurrentUser();
    if (currentUser) {
        addParticipantToUI(currentUser.id, currentUser.username, true);
    }

    // 添加远程参与者
    for (const userId in callState.participants) {
        const participant = callState.participants[userId];
        addParticipantToUI(userId, participant.username || '未知用户',
            callState.activeSpeakers.includes(userId));
    }
}

/**
 * 添加参与者到UI
 * @param {string} userId - 用户ID
 * @param {string} username - 用户名
 * @param {boolean} isSpeaking - 是否正在说话
 */
function addParticipantToUI(userId, username, isSpeaking = false) {
    const container = document.getElementById('participants-container');
    if (!container) return;

    // 创建参与者元素
    const participantEl = document.createElement('div');
    participantEl.className = `participant ${isSpeaking ? 'speaking' : ''}`;
    participantEl.id = `participant-${userId}`;

    participantEl.innerHTML = `
        <img src="/static/img/default-avatar.png" alt="${username}">
        <div class="name">${username}</div>
    `;

    container.appendChild(participantEl);
}

/**
 * 管理活跃发言者
 * @param {string} userId - 用户ID
 * @param {boolean} isActive - 是否活跃
 */
function manageSpeaker(userId, isActive) {
    if (isActive) {
        // 添加到活跃发言者列表
        if (!callState.activeSpeakers.includes(userId)) {
            callState.activeSpeakers.push(userId);
        }
    } else {
        // 从活跃发言者列表中移除
        const index = callState.activeSpeakers.indexOf(userId);
        if (index !== -1) {
            callState.activeSpeakers.splice(index, 1);
        }
    }

    // 更新UI
    updateSpeakerUI(userId, isActive);
}

/**
 * 更新发言者UI
 * @param {string} userId - 用户ID
 * @param {boolean} isSpeaking - 是否正在说话
 */
function updateSpeakerUI(userId, isSpeaking) {
    if (!callState.isGroupCall) return;

    const participantEl = document.getElementById(`participant-${userId}`);
    if (participantEl) {
        if (isSpeaking) {
            participantEl.classList.add('speaking');
        } else {
            participantEl.classList.remove('speaking');
        }
    }
}

/**
 * 更新本地发言者UI
 * @param {boolean} isSpeaking - 是否正在说话
 */
function updateLocalSpeakerUI(isSpeaking) {
    const currentUser = getCurrentUser();
    if (!currentUser) return;

    // 如果麦克风已静音，则不显示正在说话状态
    const effectiveSpeakingState = callState.isMuted ? false : isSpeaking;

    // 更新本地用户的UI
    updateSpeakerUI(currentUser.id, effectiveSpeakingState);

    // 如果有麦克风按钮，也可以更新麦克风按钮的状态
    const micBtn = document.getElementById('mic-toggle-btn');
    if (micBtn && !callState.isMuted) {
        if (isSpeaking) {
            micBtn.classList.add('active');
        } else {
            micBtn.classList.remove('active');
        }
    }
}

/**
 * 切换麦克风状态
 * @returns {Promise<boolean>} 操作是否成功
 */
async function toggleMicrophone() {
    if (!trtc || !callState.isInCall) return false;

    try {
        const currentUser = getCurrentUser();
        if (!currentUser) return false;

        if (callState.isMuted) {
            // 取消静音
            await trtc.startLocalAudio();
            callState.isMuted = false;
            updateMicrophoneUI(false);

            // // 通知其他用户
            // if (callState.isGroupCall && callState.chatroomId) {
            //     sendChatMessageToChatroom(
            //         currentUser.id,
            //         `已取消静音`,
            //         callState.chatroomId
            //     );
            // }

            return true;
        } else {
            // 静音
            await trtc.stopLocalAudio();
            callState.isMuted = true;
            updateMicrophoneUI(true);

            // 当静音时，确保用户显示为不在说话状态
            updateLocalSpeakerUI(false);

            // 从活跃发言者列表中移除自己
            manageSpeaker(currentUser.id, false);

            // // 通知其他用户
            // if (callState.isGroupCall && callState.chatroomId) {
            //     sendChatMessageToChatroom(
            //         currentUser.id,
            //         `已静音`,
            //         callState.chatroomId
            //     );
            // }

            return true;
        }
    } catch (error) {
        console.error('切换麦克风状态失败:', error);
        return false;
    }
}

/**
 * 更新麦克风UI
 * @param {boolean} isMuted - 是否静音
 */
function updateMicrophoneUI(isMuted) {
    const micBtn = document.getElementById('mic-toggle-btn');
    if (micBtn) {
        if (isMuted) {
            micBtn.classList.add('muted');
            micBtn.title = '取消静音';
            micBtn.innerHTML = '<span class="mic-icon">🔇</span><span>取消静音</span>';
        } else {
            micBtn.classList.remove('muted');
            micBtn.title = '静音';
            micBtn.innerHTML = '<span class="mic-icon">🔊</span><span>静音</span>';
        }
    }
}


/**
 * 显示来电界面
 * @param {string} callerName - 来电者名称
 * @param {string} callType - 通话类型（语音通话或群组语音通话）
 */
function showIncomingCallUI(callerName, callType = '语音通话') {
    elements.callerName.textContent = callerName;
    elements.incomingCallModal.style.display = 'flex';

    // 判断是否为群组通话
    const isGroupCall = callType.includes('群组语音通话');

    // 设置来电标题
    const incomingCallTitle = document.getElementById('incoming-call-title');
    if (incomingCallTitle) {
        if (isGroupCall) {
            // 如果callType包含群组名称，则提取出来显示在标题中
            const groupName = callState.groupName || '';
            incomingCallTitle.textContent = groupName ? `${groupName} - 群组来电` : '群组来电';
        } else {
            incomingCallTitle.textContent = '来电';
        }
    }

    // 设置来电者状态文本
    const callStatusElement = elements.incomingCallModal.querySelector('.call-status');
    if (callStatusElement) {
        if (isGroupCall) {
            // 如果有群组名称，则在状态文本中显示
            const groupName = callState.groupName || '';
            const groupText = groupName ? `加入 ${groupName} 的` : '';
            callStatusElement.innerHTML = `<span id="caller-name">${callerName}</span>邀请你${groupText}群组通话...`;
        } else {
            callStatusElement.innerHTML = `<span id="caller-name">${callerName}</span>正在呼叫你...`;
        }
    }

    // 设置来电者头像
    const callerAvatar = document.getElementById('caller-avatar');
    if (callerAvatar) {
        // 这里可以根据用户ID获取头像
        callerAvatar.src = '/static/img/default-avatar.png';
    }
}

/**
 * 开始计时器
 * @param {string} type - 计时器类型，'waiting' 或 'call'
 */
function startTimer(type) {
    // 停止所有计时器
    stopAllTimers();

    // 重置计时器
    const timerElement = document.getElementById('call-timer');
    if (!timerElement) return;

    let seconds = 0;
    timerElement.textContent = '00:00';

    // 创建计时器
    const timerInterval = setInterval(() => {
        seconds++;
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }, 1000);

    // 根据类型存储计时器引用
    if (type === 'waiting') {
        callState.waitingTimerInterval = timerInterval;
    } else if (type === 'call') {
        callState.timerInterval = timerInterval;
    }
}

/**
 * 停止所有计时器
 */
function stopAllTimers() {
    // 停止等待计时器
    if (callState.waitingTimerInterval) {
        clearInterval(callState.waitingTimerInterval);
        callState.waitingTimerInterval = null;
    }

    // 停止通话计时器
    if (callState.timerInterval) {
        clearInterval(callState.timerInterval);
        callState.timerInterval = null;
    }
}

/**
 * 开始等待计时器
 */
function startWaitingTimer() {
    startTimer('waiting');
}

/**
 * 开始通话计时器
 */
function startCallTimer() {
    startTimer('call');
}

/**
 * 启动通话超时计时器
 * @param {number} timeout - 超时时间（毫秒）
 */
function startCallTimeoutTimer(timeout = 60000) { // 默认60秒
    // 清除之前的超时计时器
    if (callState.callTimeoutTimer) {
        clearTimeout(callState.callTimeoutTimer);
        callState.callTimeoutTimer = null;
    }

    // 设置新的超时计时器
    callState.callTimeoutTimer = setTimeout(() => {
        // 检查是否仍在等待接听（没有参与者加入）
        if (callState.isInCall && Object.keys(callState.participants).length === 0) {
            // 显示超时消息
            const currentUser = getCurrentUser();
            if (currentUser) {
                const message = callState.isGroupCall ?
                    '群组通话无人接听，已自动结束' :
                    '对方未接听，通话已自动结束';

                if (callState.isGroupCall && callState.chatroomId) {
                    sendChatMessageToChatroom(currentUser.id, message, callState.chatroomId);
                } else {
                    sendChatMessageAsUser(currentUser.id, message);
                }
            }

            // 结束通话
            endCall(true, false);
        }
    }, timeout);
}

// 铃声管理
const ringtoneManager = {
    // 预加载铃声
    preloaded: null,
    // 当前播放的铃声
    current: null,
    // 点击事件监听器
    clickListener: null,

    /**
     * 初始化铃声
     */
    init() {
        // 预加载铃声
        if (!this.preloaded) {
            this.preloaded = new Audio('/static/audio/ringtone.mp3');
            this.preloaded.load(); // 预加载音频
        }
    },

    /**
     * 播放铃声
     */
    play() {
        // 如果通话状态无效，不播放铃声
        if (callState.remoteUserIds.length === 0 || !callState.roomId) {
            return;
        }

        // 尝试播放预加载的铃声
        this.playPreloaded()
            .catch(() => this.playNewInstance())
            .catch(() => this.setupClickToPlay());
    },

    /**
     * 播放预加载的铃声
     * @returns {Promise} 播放结果
     */
    async playPreloaded() {
        if (!this.preloaded) {
            this.init();
        }

        this.preloaded.currentTime = 0;
        this.preloaded.loop = true;
        this.preloaded.volume = 1.0;

        await this.preloaded.play();
        this.current = this.preloaded;
    },

    /**
     * 播放新的铃声实例
     * @returns {Promise} 播放结果
     */
    async playNewInstance() {
        const ringtone = new Audio('/static/audio/ringtone.mp3');
        ringtone.loop = true;
        ringtone.volume = 1.0;

        await ringtone.play();
        this.current = ringtone;
    },

    /**
     * 设置点击播放
     */
    setupClickToPlay() {
        // 用户交互后再尝试播放
        const playOnClick = async () => {
            // 检查通话状态
            if (callState.remoteUserIds.length === 0 || !callState.roomId) {
                this.removeClickListener();
                return;
            }

            try {
                await this.playNewInstance();
            } catch (error) {
                console.warn('用户交互后仍无法播放铃声:', error);
            }

            this.removeClickListener();
        };

        // 存储并添加事件监听器
        this.clickListener = playOnClick;
        document.addEventListener('click', playOnClick, { once: true });
    },

    /**
     * 移除点击事件监听器
     */
    removeClickListener() {
        if (this.clickListener) {
            document.removeEventListener('click', this.clickListener);
            this.clickListener = null;
        }
    },

    /**
     * 停止铃声
     */
    stop() {
        // 停止当前铃声
        if (this.current) {
            try {
                this.current.pause();
                this.current.currentTime = 0;
            } catch (e) {
                console.error('停止铃声时出错:', e);
            }
            this.current = null;
        }

        // 确保预加载铃声也停止
        if (this.preloaded) {
            try {
                this.preloaded.pause();
                this.preloaded.currentTime = 0;
            } catch (e) {}
        }

        // 移除点击事件监听器
        this.removeClickListener();
    }
};

/**
 * 播放来电铃声
 */
function playRingtone() {
    ringtoneManager.play();
}

/**
 * 停止来电铃声
 */
function stopRingtone() {
    ringtoneManager.stop();
}

/**
 * 重置通话状态
 */
function resetCallState() {
    // 停止所有计时器
    stopAllTimers();

    // 停止铃声（确保铃声资源被释放）
    stopRingtone();

    // 重置所有状态变量
    Object.assign(callState, {
        isInCall: false,
        isCaller: false,
        remoteUserIds: [],
        isGroupCall: false,
        roomId: null,
        numericRoomId: null,
        chatroomId: null,
        groupName: '',  // 重置群组名称
        ringtone: null,
        ringtoneClickListener: null,
        waitingTimerInterval: null,
        timerInterval: null,
        callTimeoutTimer: null,
        isMuted: false, // 重置麦克风状态
        participants: {},
        activeSpeakers: []
    });

    // 确保DOM元素状态重置
    resetUIState();
}

/**
 * 重置UI状态
 */
function resetUIState() {
    // 重置通话状态文本
    if (elements.callStatus) {
        elements.callStatus.textContent = '';
    }

    // 重置通话计时器
    const timerElement = document.getElementById('call-timer');
    if (timerElement) {
        timerElement.textContent = '00:00';
    }

    // 清空参与者容器
    const participantsContainer = document.getElementById('participants-container');
    if (participantsContainer) {
        participantsContainer.innerHTML = '';
    }
}

/**
 * 生成房间ID
 * @param {string} userId1 - 用户1 ID
 * @param {string} userId2 - 用户2 ID
 * @returns {string} 房间ID
 */
function generateRoomId(userId1, userId2) {
    // 确保顺序一致，以便双方生成相同的房间ID
    const sortedIds = [userId1, userId2].sort();
    // 使用时间戳确保唯一性
    return Math.abs(hashCode(`${sortedIds[0]}_${sortedIds[1]}_${Date.now()}`)) % 4294967295;
}

/**
 * 简单的字符串哈希函数
 * @param {string} str - 输入字符串
 * @returns {number} 哈希值
 */
function hashCode(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
    }
    return hash;
}

/**
 * 获取TRTC UserSig
 * @param {string} userId - 用户ID
 * @returns {Promise<string>} UserSig
 * @throws {Error} 如果获取失败则抛出错误
 */
async function getTRTCUserSig(userId) {
    try {
        const response = await fetch(`/api/trtc/get_user_sig?user_id=${userId}`);
        if (!response.ok) {
            throw new Error(`获取UserSig失败: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        if (!data.userSig) {
            throw new Error('服务器返回的UserSig无效');
        }
        return data.userSig;
    } catch (error) {
        console.error('获取UserSig失败:', error);
        // 不再返回测试值，而是抛出错误以便上层处理
        throw new Error(`无法获取通话凭证: ${error.message}`);
    }
}

/**
 * 发送通话信号
 * @param {string} signalType - 信号类型 (call_request, call_accepted, call_rejected, call_ended)
 * @param {string} recipientId - 接收者ID
 * @param {string} roomId - 房间ID
 * @param {Object} additionalData - 附加数据
 * @returns {boolean} 是否成功发送
 */
function sendCallSignal(signalType, recipientId, roomId, additionalData = {}) {
    const currentUser = getCurrentUser();
    if (!currentUser) {
        console.error(`无法发送${signalType}信号: 用户未登录`);
        return false;
    }

    // 构建基本消息结构
    const message = {
        type: signalType,
        sender: currentUser.id,
        recipient: recipientId,
        roomId: roomId,
        timestamp: Date.now(),
        ...additionalData
    };

    // 如果是通话请求，添加发送者名称
    if (signalType === 'call_request') {
        message.sender_name = currentUser.username;
    }

    // 使用现有的WebSocket发送
    return sendChatMessage(
        { is_group: false, members: [currentUser.id, recipientId] },
        JSON.stringify(message),
        currentUser.id,
        false,
        'call_signal'
    );
}

/**
 * 发送群组通话结束信号
 * @param {Array<string>} recipientIds - 接收者ID数组
 * @param {string} roomId - 房间ID
 * @returns {boolean} 是否成功发送
 */
function sendGroupCallEnded(recipientIds, roomId) {
    const currentUser = getCurrentUser();
    if (!currentUser) {
        console.error(`无法发送群组通话结束信号: 用户未登录`);
        return false;
    }

    // 向每个成员发送通话结束信号
    let allSuccess = true;
    for (const recipientId of recipientIds) {
        // 构建群组通话结束消息
        const message = {
            type: 'call_ended',
            sender: currentUser.id,
            sender_name: currentUser.username || currentUser.id,
            recipient: recipientId,
            roomId: roomId,
            timestamp: Date.now(),
            is_group_call: true
        };

        // 使用现有的WebSocket发送
        const success = sendChatMessage(
            { is_group: false, members: [currentUser.id, recipientId] },
            JSON.stringify(message),
            currentUser.id,
            false,
            'call_signal'
        );

        if (!success) {
            allSuccess = false;
        }
    }

    return allSuccess;
}

/**
 * 发送通话请求
 * @param {string} recipientId - 接收者ID
 * @param {string} roomId - 房间ID
 */
function sendCallRequest(recipientId, roomId) {
    const result = sendCallSignal('call_request', recipientId, roomId);
    console.log('通话请求发送结果:', result ? '成功' : '失败');
}

/**
 * 发送接受通话消息
 * @param {string} recipientId - 接收者ID
 * @param {string} roomId - 房间ID
 * @param {boolean} isGroupCall - 是否为群组通话
 */
function sendCallAccepted(recipientId, roomId, isGroupCall = false) {
    const currentUser = getCurrentUser();
    const additionalData = {};

    if (isGroupCall) {
        additionalData.is_group_call = true;
        additionalData.sender_name = currentUser ? currentUser.username : '';
    }

    sendCallSignal('call_accepted', recipientId, roomId, additionalData);
}

/**
 * 发送拒绝通话消息
 * @param {string} recipientId - 接收者ID
 * @param {string} roomId - 房间ID
 * @param {string} reason - 拒绝原因
 */
function sendCallRejected(recipientId, roomId, reason) {
    sendCallSignal('call_rejected', recipientId, roomId, { reason });
}

/**
 * 发送结束通话消息
 * @param {string} recipientId - 接收者ID
 * @param {string} roomId - 房间ID
 */
function sendCallEnded(recipientId, roomId) {
    sendCallSignal('call_ended', recipientId, roomId);
}

/**
 * 启用/禁用语音通话按钮
 * @param {boolean} enabled - 是否启用
 */
export function enableVoiceCallButton(enabled) {
    if (elements.callBtn) {
        elements.callBtn.disabled = !enabled;
    }
}
