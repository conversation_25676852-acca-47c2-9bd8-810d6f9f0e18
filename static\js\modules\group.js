/**
 * 群组管理模块
 */
import { DEFAULT_AVATAR, POLLING_INTERVAL, MAX_POLLING_ATTEMPTS } from './config.js';
import { elements } from './dom.js';
import { getCurrentUser, renderUserSearchResults } from './user.js';
import { createGroup } from './websocket.js';
import { 
    getSelectedMembers, 
    setSelectedMembers, 
    addSelectedMember,
    updateSelectedMembers,
    selectChatroom,
    getAllChatrooms 
} from './chat.js';

// =============== 公共 API ===============

/**
 * 创建新群组
 * 验证群组名称和成员，发送创建请求，清理UI并监控创建结果
 */
export function createNewGroup() {
    try {
        const { name, members } = validateGroupData();
        const currentUser = ensureCurrentUser();
        const createdGroupMembers = getUniqueMembers([...members, currentUser]);
        
        // 发送创建群组请求
        createGroup(name, createdGroupMembers.map(m => m.id), currentUser.id);
        
        // 清理UI并监控创建结果
        cleanupGroupCreation();
        watchGroupCreation(name, createdGroupMembers);
        
    } catch (error) {
        handleGroupError('创建群组失败', error);
    }
}

/**
 * 搜索用户并显示结果（用于群组成员选择）
 * @param {string} keyword - 搜索关键字
 */
export async function searchGroupUsers(keyword) {
    if (!keyword?.trim()) return;
    
    try {
        const users = await fetchUsersByKeyword(keyword);
        if (users) {
            // 过滤掉已选择的成员
            const existingMemberIds = new Set(getSelectedMembers().map(m => m.id));
            const filteredData = users.filter(user => !existingMemberIds.has(user.id));
            
            // 渲染搜索结果
            renderUserSearchResults(
                filteredData,
                elements.groupUserSearchResults,
                addUserToSelectedMembers
            );
        }
    } catch (error) {
        handleGroupError('搜索用户失败', error);
    }
}

// =============== 群组数据验证 ===============

/**
 * 验证群组数据
 * @returns {Object} 包含验证后的群组名称和成员列表
 * @throws {Error} 如果数据无效
 */
function validateGroupData() {
    const name = elements.groupName.value.trim();
    const members = getSelectedMembers();
    
    if (!name) {
        throw new Error('请输入群组名称');
    }
    
    if (members.length === 0) {
        throw new Error('请至少选择一名群组成员');
    }
    
    return { name, members };
}

/**
 * 确保当前用户已登录
 * @returns {Object} 当前用户对象
 * @throws {Error} 如果未获取到当前用户
 */
function ensureCurrentUser() {
    const currentUser = getCurrentUser();
    if (!currentUser) {
        throw new Error('未获取到当前用户信息');
    }
    return currentUser;
}

// =============== 群组创建和管理 ===============

/**
 * 去除重复成员
 * @param {Array} members - 成员列表
 * @returns {Array} 去重后的成员列表
 */
function getUniqueMembers(members) {
    return [...new Set(members.map(m => m.id))].map(id => 
        members.find(m => m.id === id)
    );
}

/**
 * 清理群组创建相关UI
 */
function cleanupGroupCreation() {
    elements.newGroupModal.style.display = 'none';
    elements.groupName.value = '';
    elements.groupUserSearch.value = '';
    elements.groupUserSearchResults.innerHTML = '';
    setSelectedMembers([]);
}

/**
 * 监控群组创建结果
 * @param {string} groupName - 群组名称
 * @param {Array} members - 成员列表
 */
function watchGroupCreation(groupName, members) {
    let attempts = 0;
    const maxAttempts = MAX_POLLING_ATTEMPTS || 5;
    const interval = POLLING_INTERVAL || 1000;

    const checkGroup = () => {
        const found = findAndSelectNewGroup(groupName, members.map(m => m.id));
        if (!found && attempts < maxAttempts) {
            attempts++;
            setTimeout(checkGroup, interval);
        }
    };

    setTimeout(checkGroup, interval);
}

/**
 * 查找并选择新创建的群组
 * @param {string} groupName - 群组名称
 * @param {Array} members - 成员ID数组
 * @returns {boolean} 是否找到并选择了群组
 */
function findAndSelectNewGroup(groupName, members) {
    const memberSet = new Set(members);
    const currentChatrooms = getAllChatrooms();
    
    const newGroup = currentChatrooms.find(chatroom => {
        if (!chatroom.is_group || chatroom.name !== groupName) return false;
        
        const chatroomMemberSet = new Set(chatroom.members);
        return (
            memberSet.size === chatroomMemberSet.size &&
            [...memberSet].every(id => chatroomMemberSet.has(id))
        );
    });
    
    if (newGroup) {
        console.log("找到新创建的群组:", newGroup);
        selectChatroom(newGroup);
        return true;
    }
    return false;
}

// =============== 用户搜索和选择 ===============

/**
 * 通过关键字搜索用户
 * @param {string} keyword - 搜索关键字
 * @returns {Promise<Array|null>} 搜索结果
 */
async function fetchUsersByKeyword(keyword) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    
    try {
        const response = await fetch(
            `/api/chat/users/search?keyword=${encodeURIComponent(keyword.trim())}`,
            { signal: controller.signal }
        );
        
        if (!response.ok) {
            throw new Error(`搜索失败: ${response.status}`);
        }
        
        const { code, data } = await response.json();
        return code === 200 ? data : null;
    } finally {
        clearTimeout(timeoutId);
    }
}

/**
 * 将用户添加到已选成员列表
 * @param {Object} user - 用户对象
 */
function addUserToSelectedMembers(user) {
    addSelectedMember(user);
}

// =============== 错误处理 ===============

/**
 * 处理群组操作中的错误
 * @param {string} context - 错误上下文
 * @param {Error} error - 错误对象
 */
function handleGroupError(context, error) {
    if (error.name === 'AbortError') {
        console.warn(`${context}: 请求超时`);
    } else {
        console.error(`${context}:`, error);
        alert(error.message || `${context}`);
    }
} 