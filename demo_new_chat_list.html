<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新聊天列表样式演示</title>
    <link rel="stylesheet" href="static/css/style.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .demo-header {
            background: #4a6ee0;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
        }
        .demo-description {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            新聊天列表样式演示
        </div>
        <div class="demo-description">
            展示按任务分组的聊天列表，包含任务标题、管理员信息和成员类型显示
        </div>
        
        <!-- 聊天列表 -->
        <div class="chat-list" id="chat-list">
            <!-- 任务1 -->
            <div class="task-header">
                <div class="task-title">机器人建模</div>
                <div class="task-info">
                    <span class="task-admin">管理员: limin</span>
                    <span class="task-chat-count">2个聊天室</span>
                </div>
            </div>
            
            <!-- 聊天室1 -->
            <div class="chat-item chat-item-indented" data-id="94">
                <div class="chat-avatar">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU1RTUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+" alt="头像">
                </div>
                <div class="chat-info">
                    <div class="chat-name">聊天室2</div>
                    <div class="chat-last-message">测试3</div>
                </div>
                <div class="unread-badge">3</div>
            </div>
            
            <!-- 聊天室2 -->
            <div class="chat-item chat-item-indented active" data-id="93">
                <div class="chat-avatar">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU1RTUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+" alt="头像">
                </div>
                <div class="chat-info">
                    <div class="chat-name">聊天室1</div>
                    <div class="chat-last-message">测试1</div>
                </div>
            </div>
            
            <!-- 任务2 -->
            <div class="task-header">
                <div class="task-title">网站设计项目</div>
                <div class="task-info">
                    <span class="task-admin">管理员: 张三</span>
                    <span class="task-chat-count">1个聊天室</span>
                </div>
            </div>
            
            <!-- 聊天室3 -->
            <div class="chat-item chat-item-indented" data-id="95">
                <div class="chat-avatar">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU1RTUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+" alt="头像">
                </div>
                <div class="chat-info">
                    <div class="chat-name">设计讨论组</div>
                    <div class="chat-last-message">UI设计稿已完成</div>
                </div>
            </div>
            
            <!-- 任务3 -->
            <div class="task-header">
                <div class="task-title">移动应用开发</div>
                <div class="task-info">
                    <span class="task-admin">管理员: 李四</span>
                    <span class="task-chat-count">0个聊天室</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加点击交互
        document.querySelectorAll('.chat-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有活动状态
                document.querySelectorAll('.chat-item').forEach(i => i.classList.remove('active'));
                // 添加当前活动状态
                this.classList.add('active');
                
                // 移除未读标记
                const badge = this.querySelector('.unread-badge');
                if (badge) {
                    badge.style.display = 'none';
                    this.classList.remove('unread');
                }
            });
        });
    </script>
</body>
</html>
