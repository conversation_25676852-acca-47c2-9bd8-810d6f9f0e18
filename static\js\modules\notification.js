/**
 * 通知管理模块 - 处理通知UI展示
 */
import { DEFAULT_AVATAR } from './config.js';
import { getUnreadCount, resetUnreadCount } from './message-status.js';

// =============== UI相关函数 ===============
/**
 * 渲染聊天列表项
 * @param {Object} chatroom - 聊天室对象
 * @param {string} displayName - 聊天室显示名称
 * @param {boolean} isActive - 是否为当前活动聊天室
 * @param {function} onClickCallback - 点击回调函数
 * @returns {HTMLElement} 聊天列表项元素
 */
export function renderChatListItem(chatroom, displayName, isActive, onClickCallback) {
    // 获取最后一条消息的显示文本
    const lastMessageText = formatLastMessage(chatroom.last_message);

    // 创建聊天列表项元素（先使用0作为未读计数，后面再更新）
    const chatItem = createChatItemElement(
        chatroom.id,
        displayName,
        lastMessageText,
        isActive,
        0
    );

    // 绑定点击事件
    chatItem.addEventListener('click', () => {
        onClickCallback(chatroom);
    });

    // 获取未读计数并更新UI
    const unreadCount = getUnreadCount(chatroom.id);
    if (unreadCount > 0 && !isActive) {
        // 更新未读标记
        let badge = chatItem.querySelector('.unread-badge');
        if (!badge) {
            badge = document.createElement('div');
            badge.className = 'unread-badge';
            chatItem.appendChild(badge);
        }
        badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
        badge.style.display = 'block';

        // 添加未读类
        chatItem.classList.add('unread');
    }

    return chatItem;
}

/**
 * 更新聊天列表项的最后一条消息
 * @param {string} chatroomId - 聊天室ID
 * @param {Object} message - 消息对象
 */
export function updateChatItemLastMessage(chatroomId, message) {
    const chatItem = document.querySelector(`.chat-item[data-id="${chatroomId}"]`);
    if (!chatItem) return;

    // 更新最后一条消息文本
    updateLastMessageText(chatItem, message);

    // 将聊天移到顶部并添加高亮动画
    moveChatToTop(chatroomId);
}

/**
 * 将聊天室移动到聊天列表顶部
 * @param {string} chatroomId - 聊天室ID
 */
export function moveChatToTop(chatroomId) {
    const chatList = document.getElementById('chat-list');
    const chatItem = document.querySelector(`.chat-item[data-id="${chatroomId}"]`);

    if (!chatList || !chatItem) return;

    // 添加高亮动画
    addHighlightAnimation(chatItem);

    // 移到列表顶部
    chatList.insertBefore(chatItem, chatList.firstChild);
}

// =============== 消息格式化 ===============

/**
 * 格式化最后一条消息
 * @param {Object} message - 消息对象
 * @returns {string} 格式化后的消息文本
 */
function formatLastMessage(message) {
    if (!message) return '无消息';

    // 检查是否为图片消息
    if (message.is_html && message.content.includes('<img')) {
        return '[图片消息]';
    }

    // 检查是否为卡片消息
    if (message.is_html && message.message_type && message.message_type.startsWith('card_')) {
        const cardType = message.message_type.replace('card_', '');
        switch (cardType) {
            case 'task':
                return '[任务详情卡片]';
            case 'quote':
                return '[报价单卡片]';
            case 'confirmation':
                return '[细节确认卡片]';
            default:
                return '[卡片消息]';
        }
    }

    return message.content;
}

// =============== 元素创建 ===============

/**
 * 创建聊天列表项元素
 * @param {string} chatroomId - 聊天室ID
 * @param {string} displayName - 显示名称
 * @param {string} lastMessageText - 最后一条消息文本
 * @param {boolean} isActive - 是否为活动聊天室
 * @param {number} unreadCount - 未读消息数量
 * @returns {HTMLElement} 聊天列表项元素
 */
function createChatItemElement(chatroomId, displayName, lastMessageText, isActive, unreadCount) {
    const chatItem = document.createElement('div');
    chatItem.className = 'chat-item';
    chatItem.dataset.id = chatroomId;

    // 设置活动状态
    if (isActive) {
        chatItem.classList.add('active');
    }

    // 设置未读状态
    if (unreadCount > 0 && !isActive) {
        chatItem.classList.add('unread');
    }

    // 构建HTML结构
    chatItem.innerHTML = `
        <div class="chat-avatar">
            <img src="${DEFAULT_AVATAR}" alt="头像">
        </div>
        <div class="chat-info">
            <div class="chat-name">${displayName}</div>
            <div class="chat-last-message">${lastMessageText}</div>
        </div>
        ${unreadCount > 0 && !isActive ? `<div class="unread-badge">${unreadCount}</div>` : ''}
    `;

    return chatItem;
}

// =============== UI更新函数 ===============

/**
 * 移除未读指示器
 * @param {string} chatroomId - 聊天室ID
 * @deprecated 使用resetUnreadCount代替
 */
export function removeUnreadIndicators(chatroomId) {
    // 使用resetUnreadCount重置未读计数
    resetUnreadCount(chatroomId);
}

/**
 * 更新最后一条消息文本
 * @param {HTMLElement} chatItem - 聊天列表项元素
 * @param {Object} message - 消息对象
 */
function updateLastMessageText(chatItem, message) {
    const lastMessage = chatItem.querySelector('.chat-last-message');
    if (!lastMessage) return;

    // 检查是否为图片消息
    if (message.is_html && message.content.includes('<img')) {
        lastMessage.textContent = '[图片消息]';
    }
    // 检查是否为卡片消息
    else if (message.is_html && message.message_type && message.message_type.startsWith('card_')) {
        const cardType = message.message_type.replace('card_', '');
        switch (cardType) {
            case 'task':
                lastMessage.textContent = '[任务详情卡片]';
                break;
            case 'quote':
                lastMessage.textContent = '[报价单卡片]';
                break;
            case 'confirmation':
                lastMessage.textContent = '[细节确认卡片]';
                break;
            default:
                lastMessage.textContent = '[卡片消息]';
                break;
        }
    } else {
        // 普通消息直接显示内容
        lastMessage.textContent = message.content;
    }
}



/**
 * 更新未读消息徽章
 * @param {string} chatroomId - 聊天室ID
 * @param {number} count - 未读消息数量
 */
export function updateUnreadBadge(chatroomId, count) {
    const chatItem = document.querySelector(`.chat-item[data-id="${chatroomId}"]`);
    if (!chatItem) return;

    let badge = chatItem.querySelector('.unread-badge');

    if (count > 0) {
        if (!badge) {
            badge = document.createElement('div');
            badge.className = 'unread-badge';
            chatItem.appendChild(badge);
        }
        badge.textContent = count > 99 ? '99+' : count;
        badge.style.display = 'block';

        // 只有当不是活动聊天室时，才添加unread类
        if (!chatItem.classList.contains('active')) {
            chatItem.classList.add('unread');
        }
    } else if (badge) {
        badge.style.display = 'none';
        chatItem.classList.remove('unread');
    }
}

/**
 * 添加高亮动画
 * @param {HTMLElement} chatItem - 聊天列表项元素
 */
function addHighlightAnimation(chatItem) {
    // 先移除之前的动画类，防止动画叠加
    chatItem.classList.remove('highlight-animation');

    // 强制重绘
    void chatItem.offsetWidth;

    // 添加动画类
    chatItem.classList.add('highlight-animation');

    // 动画结束后移除类
    setTimeout(() => {
        chatItem.classList.remove('highlight-animation');
    }, 1500);
}