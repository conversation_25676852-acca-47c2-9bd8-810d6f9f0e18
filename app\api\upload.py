from fastapi import APIRouter, UploadFile, File, Depends, HTTPException, Form
from fastapi.responses import JSONResponse
from app.utils.oss_util import oss_util
from app.database import get_db
from app.models import User
from sqlalchemy.orm import Session
import base64
import io

router = APIRouter()

@router.post("/upload_image")
async def upload_image(
    file: UploadFile = File(...),
    user_id: str = Form(...),
    db: Session = Depends(get_db)
):
    """
    上传图片到阿里云OSS
    """
    # 验证用户
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=401, detail="用户未认证")

    # 读取文件内容
    contents = await file.read()

    # 获取文件扩展名
    filename = file.filename
    ext = filename.split(".")[-1] if "." in filename else "jpg"

    # 上传到OSS
    image_url = oss_util.upload_image(contents, f".{ext}")

    if not image_url:
        raise HTTPException(status_code=500, detail="上传图片失败")

    return {"url": image_url}

@router.post("/upload_base64_image")
async def upload_base64_image(
    image_data: str = Form(...),
    user_id: str = Form(...),
    db: Session = Depends(get_db)
):
    """
    上传Base64编码的图片到阿里云OSS
    """
    # 验证用户
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=401, detail="用户未认证")

    try:
        # 解析Base64数据
        if "," in image_data:
            # 处理 "data:image/jpeg;base64,/9j/4AAQSkZ..." 格式
            format_info, base64_str = image_data.split(",", 1)
            if "image/png" in format_info:
                ext = ".png"
            elif "image/gif" in format_info:
                ext = ".gif"
            else:
                ext = ".jpg"  # 默认为jpg
        else:
            # 纯Base64字符串
            base64_str = image_data
            ext = ".jpg"  # 默认为jpg

        # 检查数据大小
        data_size = len(base64_str) * 0.75  # Base64字符串大小约为实际大小的1.33倍
        print(f"图片大小约为: {data_size / 1024 / 1024:.2f}MB")

        # 解码Base64数据
        image_binary = base64.b64decode(base64_str)

        # 上传到OSS
        image_url = oss_util.upload_image(image_binary, ext)

        if not image_url:
            raise HTTPException(status_code=500, detail="上传图片失败")

        return {"url": image_url}
    except Exception as e:
        print(f"处理图片失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理图片失败: {str(e)}")
