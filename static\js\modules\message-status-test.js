/**
 * 测试用的消息状态管理模块
 */

// 存储未读消息计数
let unreadCounts = {};

/**
 * 获取聊天室未读计数
 * @param {string|number} chatroomId - 聊天室ID
 * @returns {number} 未读消息数量
 */
export function getUnreadCount(chatroomId) {
    return unreadCounts[chatroomId] || 0;
}

/**
 * 重置聊天室未读计数
 * @param {string|number} chatroomId - 聊天室ID
 */
export function resetUnreadCount(chatroomId) {
    unreadCounts[chatroomId] = 0;
}

/**
 * 处理未读消息计数
 * @param {Object} data - 未读消息计数数据
 */
export function handleUnreadCounts(data) {
    // 测试用空实现
}

/**
 * 处理离线消息
 * @param {Object} data - 离线消息数据
 */
export function handleOfflineMessages(data) {
    // 测试用空实现
}
