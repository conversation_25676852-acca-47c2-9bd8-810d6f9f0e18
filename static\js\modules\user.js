/**
 * 用户相关功能模块
 */
import { DEFAULT_AVATAR } from './config.js';
import { elements, hideLoginForm } from './dom.js';

// 当前用户信息
let currentUser = null;

/**
 * 获取当前用户
 * @returns {Object|null} 当前用户对象或null
 */
export function getCurrentUser() {
    return currentUser;
}

/**
 * 设置当前用户
 * @param {Object} user - 用户对象
 */
export function setCurrentUser(user) {
    currentUser = user;
    elements.username.textContent = user.username;
}

/**
 * 清除用户会话
 */
export function clearUserSession() {
    localStorage.removeItem('chat_user');
    localStorage.removeItem('session_timestamp');
    currentUser = null;

    // 更新UI
    if (elements.username) {
        elements.username.textContent = '未登录';
    }
}

/**
 * 检查并同步用户状态
 * 确保内存中的用户状态与本地存储和UI显示一致
 */
export function checkAndSyncUserState() {
    const savedUser = localStorage.getItem('chat_user');

    // 如果本地存储有用户信息，但内存中没有
    if (savedUser && !currentUser) {
        currentUser = JSON.parse(savedUser);
        if (elements.username) {
            elements.username.textContent = currentUser.username;
        }
    }
    // 如果内存中有用户信息，但UI显示不正确
    else if (currentUser && elements.username && elements.username.textContent !== currentUser.username) {
        elements.username.textContent = currentUser.username;
    }
    // 如果内存中没有用户信息，但UI显示不是未登录
    else if (!currentUser && elements.username && elements.username.textContent !== '未登录') {
        elements.username.textContent = '未登录';
    }
}

/**
 * 从本地存储加载用户
 * @returns {Object|null} 用户对象或null（如果未找到或会话过期）
 */
export function loadUserFromStorage() {
    const savedUser = localStorage.getItem('chat_user');
    if (!savedUser) return null;

    // 检查会话是否过期
    const sessionTimestamp = localStorage.getItem('session_timestamp');
    if (sessionTimestamp) {
        const now = new Date().getTime();
        const lastActivity = parseInt(sessionTimestamp, 10);

        // 如果会话超过24小时没有活动，则认为过期
        const SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24小时
        if (now - lastActivity > SESSION_TIMEOUT) {
            console.log('会话过期，需要重新登录');
            clearUserSession();
            return null;
        }
    } else {
        // 如果没有会话时间戳，创建一个
        localStorage.setItem('session_timestamp', new Date().getTime().toString());
    }

    // 解析用户数据并设置当前用户
    currentUser = JSON.parse(savedUser);

    // 更新UI中的用户名显示
    if (elements.username) {
        elements.username.textContent = currentUser.username;
    }

    return currentUser;
}

/**
 * 将用户保存到本地存储
 * @param {Object} user - 用户对象
 */
export function saveUserToStorage(user) {
    localStorage.setItem('chat_user', JSON.stringify(user));
}

/**
 * 执行登录
 * @returns {Object|null} 用户对象或null（如果登录失败）
 */
export function login() {
    const username = elements.loginUsername.value.trim();
    if (!username) return null;

    // 直接使用用户名作为ID，这样在搜索时更容易匹配
    const user = {
        id: username,
        username: username
    };

    // 保存并设置当前用户
    saveUserToStorage(user);
    setCurrentUser(user);

    // 设置会话时间戳
    localStorage.setItem('session_timestamp', new Date().getTime().toString());

    hideLoginForm();

    return user;
}

/**
 * 搜索用户
 * @param {string} keyword - 搜索关键字
 * @returns {Promise<Array>} 用户数组
 */
export async function searchUsers(keyword) {
    if (!keyword) return [];

    try {
        const response = await fetch(`/api/chat/users/search?keyword=${encodeURIComponent(keyword)}`);
        const data = await response.json();

        if (data.code === 200) {
            return data.data;
        }
        return [];
    } catch (error) {
        console.error('搜索用户失败:', error);
        return [];
    }
}

/**
 * 渲染用户搜索结果
 * @param {Array} users - 用户数组
 * @param {HTMLElement} container - 结果容器
 * @param {Function} clickHandler - 点击处理函数
 * @param {Array} excludeIds - 要排除的用户ID数组
 */
export function renderUserSearchResults(users, container, clickHandler, excludeIds = []) {
    container.innerHTML = '';

    if (users.length === 0) {
        container.innerHTML = '<div class="no-results">没有找到用户</div>';
        return;
    }

    // 过滤掉当前用户和已排除的用户
    let filteredUsers = users.filter(user =>
        user.id !== currentUser.id && !excludeIds.includes(user.id)
    );

    if (filteredUsers.length === 0) {
        container.innerHTML = '<div class="no-results">没有可添加的用户</div>';
        return;
    }

    filteredUsers.forEach(user => {
        const userItem = document.createElement('div');
        userItem.className = 'user-item';
        userItem.innerHTML = `
            <img src="${user.avatar || DEFAULT_AVATAR}" alt="头像">
            <div>${user.username}</div>
        `;

        userItem.addEventListener('click', () => {
            clickHandler(user);
        });

        container.appendChild(userItem);
    });
}