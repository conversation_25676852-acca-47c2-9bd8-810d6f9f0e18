"""
卡片消息API路由
"""
from fastapi import APIRouter, HTTPException, Depends, Body
from typing import Dict, Any, List
from app.database import get_db
from sqlalchemy.orm import Session
from utils.redis_util import get_redis
from app.models import CardConfirmation, Message
from sqlalchemy import and_
import json
from datetime import datetime

router = APIRouter()

@router.post("/confirm-card")
async def confirm_card(
    data: Dict[str, Any] = Body(...),
    db: Session = Depends(get_db),
    r = Depends(get_redis)
):
    """
    确认卡片消息
    """
    try:
        # 验证必要字段
        if not all(key in data for key in ['type', 'id', 'user_id', 'chatroom_id']):
            raise HTTPException(status_code=400, detail="缺少必要字段")

        card_id = data['id']
        card_type = data['type']
        user_id = data['user_id']
        chatroom_id = data['chatroom_id']
        message_id = data.get('message_id')

        # 检查是否已经确认过
        existing_confirmation = db.query(CardConfirmation).filter(
            and_(
                CardConfirmation.card_id == card_id,
                CardConfirmation.card_type == card_type,
                CardConfirmation.chatroom_id == chatroom_id
            )
        ).first()

        if existing_confirmation:
            # 已经确认过，返回已存在的确认信息
            return {
                "code": 200,
                "msg": "卡片已被确认",
                "data": {
                    "card_id": card_id,
                    "card_type": card_type,
                    "confirmed_by": existing_confirmation.confirmed_by,
                    "confirmed_at": existing_confirmation.confirmed_at.isoformat(),
                    "already_confirmed": True
                }
            }

        # 创建新的确认记录
        confirmation = CardConfirmation(
            card_id=card_id,
            card_type=card_type,
            message_id=message_id,
            chatroom_id=chatroom_id,
            confirmed_by=user_id,
            confirmed_at=datetime.now()
        )

        # 保存到数据库
        db.add(confirmation)
        db.commit()
        db.refresh(confirmation)

        # 准备广播消息
        ws_param = {
            "type": "card_confirmation",
            "card_id": card_id,
            "card_type": card_type,
            "user_id": user_id,
            "chatroom_id": chatroom_id,
            "timestamp": data.get('timestamp', None),
            "confirmed_at": confirmation.confirmed_at.isoformat()
        }

        # 发布消息到Redis
        await r.publish('chat', json.dumps(ws_param))

        return {
            "code": 200,
            "msg": "确认成功",
            "data": {
                "card_id": card_id,
                "card_type": card_type,
                "confirmed_by": user_id,
                "confirmed_at": confirmation.confirmed_at.isoformat(),
                "already_confirmed": False
            }
        }
    except Exception as e:
        # 回滚事务
        db.rollback()
        raise HTTPException(status_code=500, detail=f"确认卡片失败: {str(e)}")


@router.get("/card-confirmations")
async def get_card_confirmations(
    chatroom_id: int,
    card_id: str = None,
    card_type: str = None,
    db: Session = Depends(get_db)
):
    """
    获取卡片确认状态

    参数:
    - chatroom_id: 聊天室ID
    - card_id: 可选，卡片ID
    - card_type: 可选，卡片类型

    返回:
    - 卡片确认状态列表
    """
    try:
        query = db.query(CardConfirmation).filter(CardConfirmation.chatroom_id == chatroom_id)

        if card_id:
            query = query.filter(CardConfirmation.card_id == card_id)

        if card_type:
            query = query.filter(CardConfirmation.card_type == card_type)

        confirmations = query.all()

        return {
            "code": 200,
            "msg": "获取成功",
            "data": [confirmation.to_dict() for confirmation in confirmations]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取卡片确认状态失败: {str(e)}")
