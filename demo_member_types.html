<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成员类型显示演示</title>
    <link rel="stylesheet" href="static/css/style.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
        }
        .demo-header {
            background: #4a6ee0;
            color: white;
            padding: 15px 20px;
            font-weight: bold;
        }
        .demo-description {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-size: 14px;
            color: #666;
        }
        .left-panel {
            flex: 1;
            border-right: 1px solid #e9ecef;
        }
        .right-panel {
            width: 250px;
        }
        .chat-content {
            padding: 20px;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div style="text-align: center; margin-bottom: 20px;">
        <h2>成员类型显示演示</h2>
        <p>展示新的数据结构中包含的用户类型信息</p>
    </div>
    
    <div class="demo-container">
        <!-- 左侧聊天列表 -->
        <div class="left-panel">
            <div class="demo-header">
                聊天列表
            </div>
            
            <div class="chat-list">
                <!-- 任务1 -->
                <div class="task-header">
                    <div class="task-title">机器人建模</div>
                    <div class="task-info">
                        <span class="task-admin">管理员: limin</span>
                        <span class="task-chat-count">2个聊天室</span>
                    </div>
                </div>
                
                <!-- 聊天室1 -->
                <div class="chat-item chat-item-indented active" data-id="94" data-members='[{"user_id":"3229571","user_type":"1","user_type_name":"管理员"},{"user_id":"23233","user_type":"2","user_type_name":"雇主"},{"user_id":"234554","user_type":"3","user_type_name":"设计师"},{"user_id":"65654","user_type":"4","user_type_name":"普通成员"}]'>
                    <div class="chat-avatar">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU1RTUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+" alt="头像">
                    </div>
                    <div class="chat-info">
                        <div class="chat-name">聊天室2</div>
                        <div class="chat-last-message">测试3</div>
                    </div>
                </div>
                
                <!-- 聊天室2 -->
                <div class="chat-item chat-item-indented" data-id="93" data-members='[{"user_id":"3229571","user_type":"1","user_type_name":"管理员"},{"user_id":"12","user_type":"2","user_type_name":"雇主"},{"user_id":"995580","user_type":"3","user_type_name":"设计师"},{"user_id":"65985","user_type":"4","user_type_name":"普通成员"},{"user_id":"56563","user_type":"4","user_type_name":"普通成员"},{"user_id":"4656","user_type":"4","user_type_name":"普通成员"}]'>
                    <div class="chat-avatar">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU1RTUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+" alt="头像">
                    </div>
                    <div class="chat-info">
                        <div class="chat-name">聊天室1</div>
                        <div class="chat-last-message">测试1</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 右侧成员面板 -->
        <div class="right-panel">
            <div class="demo-header">
                群组成员 (4人)
            </div>
            
            <div class="members-list" id="members-list">
                <!-- 成员列表将在这里动态生成 -->
            </div>
        </div>
    </div>

    <script>
        const DEFAULT_AVATAR = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU1RTUiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzk5OTk5OSIvPgo8cGF0aCBkPSJNMzAgMzJDMzAgMjYuNDc3MSAyNS41MjI5IDIyIDIwIDIyQzE0LjQ3NzEgMjIgMTAgMjYuNDc3MSAxMCAzMkgzMFoiIGZpbGw9IiM5OTk5OTkiLz4KPC9zdmc+";
        
        function displayMembers(members) {
            const membersList = document.getElementById('members-list');
            membersList.innerHTML = '';
            
            members.forEach(member => {
                const memberItem = document.createElement('div');
                memberItem.className = 'member-item';
                memberItem.innerHTML = `
                    <img src="${DEFAULT_AVATAR}" alt="头像" class="member-avatar">
                    <div class="member-info">
                        <div class="member-name">${member.user_id}</div>
                        <div class="member-type">${member.user_type_name}</div>
                    </div>
                `;
                membersList.appendChild(memberItem);
            });
        }
        
        // 添加点击交互
        document.querySelectorAll('.chat-item').forEach(item => {
            item.addEventListener('click', function() {
                // 移除所有活动状态
                document.querySelectorAll('.chat-item').forEach(i => i.classList.remove('active'));
                // 添加当前活动状态
                this.classList.add('active');
                
                // 显示成员信息
                const membersData = this.getAttribute('data-members');
                if (membersData) {
                    const members = JSON.parse(membersData);
                    displayMembers(members);
                    
                    // 更新成员计数
                    const header = document.querySelector('.right-panel .demo-header');
                    header.textContent = `群组成员 (${members.length}人)`;
                }
            });
        });
        
        // 初始化显示第一个聊天室的成员
        const firstChatItem = document.querySelector('.chat-item.active');
        if (firstChatItem) {
            const membersData = firstChatItem.getAttribute('data-members');
            if (membersData) {
                const members = JSON.parse(membersData);
                displayMembers(members);
            }
        }
    </script>
</body>
</html>
