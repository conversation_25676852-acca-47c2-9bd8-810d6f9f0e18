from fastapi import WebSocket, WebSocketDisconnect, Depends
from sqlalchemy.orm import Session
from sqlalchemy import desc, select
from app.database import get_db, generate_id
from app.models import User, ChatRoom, Message
from app.utils.message_filter import MessageFilter
from utils.redis_queue import RedisMessageQueue
from utils.redis_util import get_redis, get_redis_instance
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import asyncio  # 添加对asyncio的导入
import time


class ConnectionManager:
    def __init__(self):
        # 保存当前所有的链接的websocket对象
        self.websocket_connections: Dict[str, WebSocket] = {}
        # 保存活跃用户
        self.active_users: Dict[str, User] = {}
        # 保存最后心跳时间
        self.last_heartbeat: Dict[str, float] = {}
        # 心跳超时时间（秒）
        self.heartbeat_timeout = 120  # 2分钟
        # 心跳检查任务
        self.heartbeat_task = None
        # 是否正在运行
        self.running = True
        # 注意：不在__init__中启动心跳检查任务，而是在应用启动事件中启动

    async def start_heartbeat_check(self):
        """启动心跳检查任务"""
        if self.heartbeat_task is None or self.heartbeat_task.done():
            self.running = True
            self.heartbeat_task = asyncio.create_task(self._check_heartbeats())
            print("心跳检查任务已启动")

    async def stop_heartbeat_check(self):
        """停止心跳检查任务"""
        self.running = False
        if self.heartbeat_task and not self.heartbeat_task.done():
            self.heartbeat_task.cancel()
            try:
                await self.heartbeat_task
            except asyncio.CancelledError:
                print("心跳检查任务已取消")
            except Exception as e:
                print(f"取消心跳检查任务时出错: {str(e)}")
        self.heartbeat_task = None

    async def _check_heartbeats(self):
        """定期检查心跳，断开超时连接"""
        try:
            while self.running:
                try:
                    # 等待一段时间，但可以被取消
                    for _ in range(30):  # 每30秒检查一次，但每秒检查一次是否被取消
                        if not self.running:
                            break
                        await asyncio.sleep(1)

                    # 如果已经不在运行，退出循环
                    if not self.running:
                        break

                    now = time.time()
                    timeout_users = []
                    active_connections = len(self.websocket_connections)

                    # 记录当前活跃连接数
                    if active_connections > 0:
                        print(f"当前活跃连接数: {active_connections}, 开始检查心跳...")

                    # 检查所有连接的心跳时间
                    for user_id, last_time in list(self.last_heartbeat.items()):
                        # 如果已经不在运行，退出循环
                        if not self.running:
                            break

                        time_since_last_heartbeat = int(now - last_time)

                        # 如果超过心跳超时时间，断开连接
                        if time_since_last_heartbeat > self.heartbeat_timeout:
                            print(f"用户 {user_id} 心跳超时 ({time_since_last_heartbeat}秒 > {self.heartbeat_timeout}秒)，断开连接")
                            timeout_users.append(user_id)
                        # 如果接近超时，记录警告
                        elif time_since_last_heartbeat > (self.heartbeat_timeout * 0.8):
                            print(f"警告: 用户 {user_id} 心跳接近超时 ({time_since_last_heartbeat}秒)")

                    # 断开超时连接
                    if timeout_users and self.running:
                        print(f"发现 {len(timeout_users)} 个超时连接，准备断开")
                        for user_id in timeout_users:
                            if self.running:  # 再次检查，以防在断开过程中被取消
                                await self.disconnect(user_id)
                        print(f"超时连接断开完成，当前活跃连接数: {len(self.websocket_connections)}")
                except asyncio.CancelledError:
                    # 任务被取消，退出循环
                    print("心跳检查任务被取消")
                    break
                except Exception as e:
                    print(f"心跳检查错误: {str(e)}")
                    # 短暂等待后继续，避免错误导致CPU占用过高
                    await asyncio.sleep(5)

            print("心跳检查任务已退出")
        except asyncio.CancelledError:
            print("心跳检查任务被取消")
            raise
        except Exception as e:
            print(f"心跳检查任务异常退出: {str(e)}")
            raise

    async def connect(self, websocket: WebSocket, user_id: str, db: Session):
        try:
            # 获取或创建用户
            user = db.execute(select(User).filter(User.id == user_id)).scalar_one_or_none()
            if not user:
                user = User(id=user_id, username=f"用户{user_id}")
                db.add(user)
                db.commit()
                db.refresh(user)

            # 添加到活跃用户列表
            self.active_users[user_id] = user

            # 添加连接并发送欢迎消息
            await websocket.accept()
            self.websocket_connections[user_id] = websocket

            # 记录初始心跳时间
            self.last_heartbeat[user_id] = time.time()

            # 发送欢迎消息
            await websocket.send_json({
                "type": "system",
                "msg": "欢迎来到沐风众包!",
                "sender": "系统消息",
                "recipient": user_id
            })

            # 发送聊天室列表
            stmt = select(ChatRoom).join(ChatRoom.members).filter(User.id == user_id)
            chatrooms = db.execute(stmt).scalars().all()
            for chatroom in chatrooms:
                print(chatroom.to_dict())
                
            await websocket.send_json({
                "type": "chatroom_list",
                "chatrooms": [chatroom.to_dict() for chatroom in chatrooms]
            })

            # 获取Redis连接
            redis = await get_redis_instance()

            # 获取未读消息计数并发送
            unread_counts = await RedisMessageQueue.get_unread_counts(redis, user_id)
            if unread_counts:
                await websocket.send_json({
                    "type": "unread_counts",
                    "counts": unread_counts
                })

            # 获取离线消息并发送
            offline_messages = await RedisMessageQueue.get_offline_messages(redis, user_id)
            if offline_messages:
                # 按聊天室分组
                chatroom_messages = {}
                for msg_info in offline_messages:
                    chatroom_id = msg_info["chatroom_id"]
                    if chatroom_id not in chatroom_messages:
                        chatroom_messages[chatroom_id] = []

                    # 从数据库获取完整消息
                    try:
                        msg = db.query(Message).filter(Message.id == msg_info["message_id"]).first()
                        if msg:
                            chatroom_messages[chatroom_id].append(msg.to_dict())
                            # 发送后从队列中移除
                            await RedisMessageQueue.remove_offline_message(redis, user_id, msg.id)
                    except Exception as e:
                        print(f"获取离线消息错误: {str(e)}, 消息ID: {msg_info['message_id']}")

                # 发送离线消息，每个聊天室一个批次
                for chatroom_id, messages in chatroom_messages.items():
                    if messages:  # 确保有消息才发送
                        await websocket.send_json({
                            "type": "offline_messages",
                            "chatroom_id": chatroom_id,
                            "messages": messages
                        })

            try:
                # 处理消息
                while True:
                    # 获取信息
                    message = await websocket.receive_json()
                    # 处理发送信息
                    try:
                        # 确保每次消息处理都使用有效的数据库会话
                        await self.handle_websocket_message(message, user_id, db)
                    except Exception as msg_error:
                        # 处理消息处理过程中的异常，但不断开连接
                        print(f"处理消息错误: {str(msg_error)}, 用户 {user_id}, 消息类型: {message.get('type', 'unknown')}")
                        # 尝试回滚数据库事务
                        try:
                            db.rollback()
                        except Exception as rollback_error:
                            print(f"回滚数据库事务错误: {str(rollback_error)}")

            except WebSocketDisconnect:
                # 连接断开时清理资源
                print(f"WebSocket断开连接: 用户 {user_id}")
            except Exception as e:
                # 处理其他异常
                print(f"WebSocket连接异常: {str(e)}, 用户 {user_id}")
        finally:
            # 无论发生什么异常，都确保断开连接并清理资源
            await self.disconnect(user_id)

            # 尝试安全地关闭数据库会话
            try:
                # 尝试回滚任何未提交的事务
                db.rollback()
            except Exception as db_error:
                print(f"关闭数据库会话错误: {str(db_error)}")
                # 这里不再抛出异常，因为我们已经在清理阶段

    async def handle_websocket_message(self, message: dict, user_id: str, db: Optional[Session] = None):
        """处理WebSocket消息

        Args:
            message: 消息内容
            user_id: 用户ID
            db: 数据库会话，如果为None，则不执行需要数据库的操作
        """
        if not db:
            print(f"警告: 处理消息时没有提供数据库会话，用户 {user_id}, 消息类型: {message.get('type', 'unknown')}")
            return

        msg_type = message.get("type", "")

        # 处理心跳消息
        if msg_type == "heartbeat":
            try:
                # 更新最后心跳时间
                current_time = time.time()
                self.last_heartbeat[user_id] = current_time

                # 获取客户端发送的时间戳
                client_timestamp = message.get("timestamp")

                # 收到心跳包，回复心跳响应
                sender_conn = self.websocket_connections.get(user_id)
                if sender_conn:
                    await sender_conn.send_json({
                        "type": "heartbeat_response",
                        "client_timestamp": client_timestamp,  # 返回客户端发送的时间戳，便于计算往返时间
                        "server_timestamp": current_time,      # 服务器时间戳
                        "timestamp": datetime.now().timestamp()  # 兼容旧版本
                    })
            except Exception as e:
                print(f"处理心跳消息错误: {str(e)}, 用户 {user_id}")
            return

        # 处理私聊消息
        if msg_type == "private_message":
            recipient_id = message.get("recipient")
            msg_content = message.get("msg")
            message_type = message.get("message_type", "")

            # 检查是否为通话信号
            if message_type == "call_signal":
                # 直接转发通话信号
                try:
                    # 解析通话信号内容
                    call_data = json.loads(msg_content)
                    call_type = call_data.get("type")

                    # 向接收者转发通话信号
                    recipient_conn = self.websocket_connections.get(recipient_id)
                    if recipient_conn:
                        # 添加日志以便调试
                        print(f"准备转发通话信号到接收者: {recipient_id}, 信号类型: {call_type}")

                        # 确保接收者收到原始的通话信号格式
                        await recipient_conn.send_json(call_data)
                        print(f"通话信号已转发成功: {call_type} 从 {user_id} 到 {recipient_id}")
                    else:
                        print(f"接收者不在线，无法转发通话信号: {recipient_id}")

                    # 向发送者发送确认
                    sender_conn = self.websocket_connections.get(user_id)
                    if sender_conn:
                        await sender_conn.send_json({
                            "type": "call_signal_sent",
                            "original_type": call_type,
                            "recipient": recipient_id,
                            "success": recipient_id in self.websocket_connections
                        })

                    return
                except Exception as e:
                    print(f"处理通话信号错误: {str(e)}")
                    # 向发送者发送错误信息
                    sender_conn = self.websocket_connections.get(user_id)
                    if sender_conn:
                        await sender_conn.send_json({
                            "type": "call_signal_error",
                            "error": str(e)
                        })
                    return

            # 检查是否为通话相关消息
            elif message_type == "call_message":
                # 检查是否为模拟发送（以其他用户身份发送）
                impersonate = message.get("impersonate", False)

                # 如果是模拟发送，使用指定的发送者ID
                actual_sender_id = message.get("sender") if impersonate else user_id

                print(f"处理通话相关消息: {msg_content}, 发送者: {actual_sender_id}")

                # 查找或创建聊天室
                chatroom = await self._get_or_create_private_chat(actual_sender_id, recipient_id, db)

                # 检查是否为HTML内容
                is_html = message.get("is_html", False)

                # 保存消息
                msg = Message(
                    content=msg_content,
                    sender_id=actual_sender_id,  # 使用实际发送者ID
                    chatroom_id=chatroom.id,
                    is_html=is_html
                )
                db.add(msg)
                db.commit()
                db.refresh(msg)

                # 向接收者发送消息
                recipient_conn = self.websocket_connections.get(recipient_id)
                if recipient_conn:
                    await recipient_conn.send_json({
                        "type": "new_message",
                        "message": msg.to_dict()
                    })

                # 向发送者回复确认
                sender_conn = self.websocket_connections.get(user_id)
                if sender_conn:
                    await sender_conn.send_json({
                        "type": "message_sent",
                        "message": msg.to_dict()
                    })

                return

            # 获取消息类型和内容类型
            message_type = message.get("message_type", "")
            content_type = message.get("content_type", "text")
            client_timestamp = message.get("timestamp")

            # 根据消息类型确定是否需要过滤内容
            should_filter = True

            # 图片和文件消息不需要过滤手机号码
            if msg_type in ["image_message", "file_message","card_confirmation","card_task","card_quote"] or content_type in ["image", "file", "card"]:
                should_filter = False

            # 过滤消息内容中的手机号码，传入用户ID以检测分段发送
            if should_filter:
                contains_phone, filtered_content, _ = MessageFilter.filter_phone_number(msg_content, user_id)
                if contains_phone:
                    msg_content = filtered_content
                    print(f"消息内容已过滤手机号码: {user_id}")

            # 查找或创建聊天室
            chatroom = await self._get_or_create_private_chat(user_id, recipient_id, db)

            # 检查是否为HTML内容
            is_html = message.get("is_html", False)

            # 根据消息类型设置数据库中的消息类型
            db_message_type = message_type
            if msg_type == "image_message":
                db_message_type = "image" if not db_message_type else db_message_type
                content_type = "image"
            elif msg_type == "file_message":
                db_message_type = "file" if not db_message_type else db_message_type
                content_type = "file"

            # 保存消息
            msg = Message(
                content=msg_content,
                sender_id=user_id,
                chatroom_id=chatroom.id,
                is_html=is_html,
                message_type=db_message_type,
                content_type=content_type
            )
            db.add(msg)
            db.commit()
            db.refresh(msg)

            # 构建消息对象，添加客户端时间戳
            message_dict = msg.to_dict()
            if client_timestamp:
                message_dict["client_timestamp"] = client_timestamp

            # 向接收者发送消息
            recipient_conn = self.websocket_connections.get(recipient_id)
            if recipient_conn:
                # 接收者在线，直接发送
                await recipient_conn.send_json({
                    "type": "new_message",
                    "message": message_dict
                })
            else:
                # 接收者离线，添加到Redis队列
                redis = await get_redis_instance()
                await RedisMessageQueue.add_offline_message(
                    redis,
                    recipient_id,
                    msg.id,
                    msg.chatroom_id
                )

            # 向发送者回复确认
            sender_conn = self.websocket_connections.get(user_id)
            if sender_conn:
                await sender_conn.send_json({
                    "type": "message_sent",
                    "message": message_dict
                })

        # 处理群聊消息
        elif msg_type == "group_message" or msg_type == "image_message" or msg_type == "file_message":
            chatroom_id = message.get("chatroom_id")
            msg_content = message.get("msg")

            # 如果是图片或文件消息，但没有指定聊天室ID，则无法处理
            if (msg_type == "image_message" or msg_type == "file_message") and not chatroom_id:
                print(f"图片或文件消息缺少聊天室ID: {msg_type}")
                return

            # 获取消息类型和内容类型
            message_type = message.get("message_type", "")
            content_type = message.get("content_type", "text")
            client_timestamp = message.get("timestamp")

            # 根据消息类型确定是否需要过滤内容
            should_filter = True

            # 图片和文件消息不需要过滤手机号码
            if msg_type in ["image_message", "file_message"] or content_type in ["image", "file"]:
                should_filter = False

            # 过滤消息内容中的手机号码，传入用户ID以检测分段发送
            if should_filter:
                contains_phone, filtered_content, _ = MessageFilter.filter_phone_number(msg_content, user_id)
                if contains_phone:
                    msg_content = filtered_content
                    print(f"群聊消息内容已过滤手机号码: {user_id}")

            # 检查是否为HTML内容
            is_html = message.get("is_html", False)

            # 根据消息类型设置数据库中的消息类型
            db_message_type = message_type
            if msg_type == "image_message":
                db_message_type = "image" if not db_message_type else db_message_type
                content_type = "image"
            elif msg_type == "file_message":
                db_message_type = "file" if not db_message_type else db_message_type
                content_type = "file"

            # 保存消息
            msg = Message(
                content=msg_content,
                sender_id=user_id,
                chatroom_id=chatroom_id,
                is_html=is_html,
                message_type=db_message_type,
                content_type=content_type
            )
            db.add(msg)
            db.commit()
            db.refresh(msg)

            # 构建消息对象，添加客户端时间戳
            message_dict = msg.to_dict()
            if client_timestamp:
                message_dict["client_timestamp"] = client_timestamp

            # 获取聊天室成员
            chatroom = db.execute(select(ChatRoom).filter(ChatRoom.id == chatroom_id)).scalar_one_or_none()
            if chatroom:
                # 获取Redis连接
                redis = await get_redis_instance()

                # 向所有成员发送消息
                for member in chatroom.members:
                    if member.id != user_id:  # 不发送给自己
                        member_conn = self.websocket_connections.get(member.id)
                        if member_conn:
                            # 成员在线，直接发送
                            await member_conn.send_json({
                                "type": "new_message",
                                "message": message_dict
                            })
                        else:
                            # 成员离线，添加到Redis队列
                            await RedisMessageQueue.add_offline_message(
                                redis,
                                member.id,
                                msg.id,
                                msg.chatroom_id
                            )

                # 向发送者回复确认
                sender_conn = self.websocket_connections.get(user_id)
                if sender_conn:
                    await sender_conn.send_json({
                        "type": "message_sent",
                        "message": message_dict
                    })

        # 处理编辑消息
        elif msg_type == "edit_message":
            message_id = message.get("message_id")
            new_content = message.get("content")

            # 过滤消息内容中的手机号码，传入用户ID以检测分段发送
            contains_phone, filtered_content, _ = MessageFilter.filter_phone_number(new_content, user_id)
            if contains_phone:
                new_content = filtered_content

            # 更新消息
            msg = db.execute(
                select(Message).filter(
                    Message.id == message_id,
                    Message.sender_id == user_id  # 只能编辑自己的消息
                )
            ).scalar_one_or_none()

            if msg:
                msg.content = new_content
                msg.updated_at = datetime.now()
                msg.is_edited = True
                db.commit()
                db.refresh(msg)

                # 获取聊天室
                chatroom = db.execute(select(ChatRoom).filter(ChatRoom.id == msg.chatroom_id)).scalar_one_or_none()
                if chatroom:
                    # 向所有在线成员发送更新
                    for member in chatroom.members:
                        if member.id in self.websocket_connections:
                            await self.websocket_connections[member.id].send_json({
                                "type": "message_updated",
                                "message": msg.to_dict()
                            })

        # 获取历史消息
        elif msg_type == "get_history":
            chatroom_id = message.get("chatroom_id")
            offset = message.get("offset", 0)
            limit = message.get("limit", 20)

            # 查询消息
            stmt = select(Message).filter(
                Message.chatroom_id == chatroom_id
            ).order_by(desc(Message.created_at)).offset(offset).limit(limit)

            # 记录查询开始时间
            start_time = time.time()

            # 执行查询
            messages = db.execute(stmt).scalars().all()

            # 向请求者发送消息
            sender_conn = self.websocket_connections.get(user_id)
            if sender_conn:
                await sender_conn.send_json({
                    "type": "history_messages",
                    "chatroom_id": chatroom_id,
                    "offset": offset,
                    "messages": [msg.to_dict() for msg in messages]
                })

            # 计算并记录查询耗时
            end_time = time.time()
            print(f"查询消息耗时: {end_time - start_time:.3f} 秒")

        # 创建群聊
        elif msg_type == "create_group":
            group_name = message.get("name")
            member_ids = message.get("members", [])

            # 确保创建者在成员列表中
            if user_id not in member_ids:
                member_ids.append(user_id)

            # 创建群聊
            chatroom = ChatRoom(
                name=group_name,
                is_group=True
            )

            # 添加成员
            for member_id in member_ids:
                member = db.execute(select(User).filter(User.id == member_id)).scalar_one_or_none()
                if member:
                    chatroom.members.append(member)

            db.add(chatroom)
            db.commit()
            db.refresh(chatroom)

            # 通知所有在线成员
            for member_id in member_ids:
                if member_id in self.websocket_connections:
                    await self.websocket_connections[member_id].send_json({
                        "type": "new_chatroom",
                        "chatroom": chatroom.to_dict()
                    })

        # 添加用户到群组
        elif msg_type == "add_to_group":
            user_to_add_id = message.get("user_id")
            group_id = message.get("group_id")

            # 检查群组存在并且当前用户在群组中
            chatroom = db.execute(select(ChatRoom).filter(
                ChatRoom.id == group_id,
                ChatRoom.is_group == True
            )).scalar_one_or_none()

            if chatroom:
                # 检查用户是否已在群组中
                member_ids = [member.id for member in chatroom.members]

                # 验证请求者是否在组内
                if user_id not in member_ids:
                    return

                # 如果用户未在群组中，添加用户
                if user_to_add_id not in member_ids:
                    user_to_add = db.execute(select(User).filter(User.id == user_to_add_id)).scalar_one_or_none()

                    if user_to_add:
                        chatroom.members.append(user_to_add)
                        db.commit()
                        db.refresh(chatroom)

                        # 系统消息通知
                        system_msg = Message(
                            content=f"{user_id} 邀请 {user_to_add_id} 加入群组",
                            sender_id="system",
                            chatroom_id=group_id
                        )
                        db.add(system_msg)
                        db.commit()
                        db.refresh(system_msg)

                        # 通知群组所有成员
                        for member in chatroom.members:
                            if member.id in self.websocket_connections:
                                # 发送群组更新通知
                                await self.websocket_connections[member.id].send_json({
                                    "type": "new_chatroom",
                                    "chatroom": chatroom.to_dict()
                                })

                                # 发送系统消息
                                await self.websocket_connections[member.id].send_json({
                                    "type": "new_message",
                                    "message": system_msg.to_dict()
                                })

        # 处理重置未读计数请求
        elif msg_type == "reset_unread_count":
            chatroom_id = message.get("chatroom_id")
            if chatroom_id:
                # 获取Redis连接
                redis = await get_redis_instance()
                # 重置未读计数
                await RedisMessageQueue.reset_unread_count(redis, user_id, int(chatroom_id))

        # 处理更新未读计数请求（用于在线消息的未读计数持久化）
        elif msg_type == "update_unread_count":
            chatroom_id = message.get("chatroom_id")
            count = message.get("count", 1)
            if chatroom_id:
                # 获取Redis连接
                redis = await get_redis_instance()
                # 更新未读计数（直接设置为指定值）
                await redis.hset(f"{RedisMessageQueue.UNREAD_COUNT_PREFIX}{user_id}", str(chatroom_id), count)
                print(f"已更新用户 {user_id} 的聊天室 {chatroom_id} 未读计数为 {count}")

        # 处理卡片确认消息
        elif msg_type == "card_confirmation":
            # 获取必要字段
            card_id = message.get("card_id")
            card_type = message.get("card_type")
            chatroom_id = message.get("chatroom_id")
            confirmed_by = message.get("user_id")
            confirmed_at = message.get("confirmed_at") or message.get("timestamp") or datetime.now().isoformat()

            if not all([card_id, card_type, chatroom_id, confirmed_by]):
                print(f"卡片确认消息缺少必要字段: {message}")
                return

            print(f"处理卡片确认消息: 卡片ID={card_id}, 类型={card_type}, 确认人={confirmed_by}, 聊天室={chatroom_id}")

            # 获取聊天室成员
            chatroom = db.execute(select(ChatRoom).filter(ChatRoom.id == chatroom_id)).scalar_one_or_none()
            if not chatroom:
                print(f"找不到聊天室: {chatroom_id}")
                return

            # 构建确认消息
            confirmation_data = {
                "type": "card_confirmation",
                "card_id": card_id,
                "card_type": card_type,
                "user_id": confirmed_by,
                "chatroom_id": chatroom_id,
                "confirmed_at": confirmed_at
            }

            # 向所有在线成员广播确认消息
            for member in chatroom.members:
                member_conn = self.websocket_connections.get(member.id)
                if member_conn:
                    try:
                        await member_conn.send_json(confirmation_data)
                        print(f"已向用户 {member.id} 发送卡片确认消息")
                    except Exception as e:
                        print(f"向用户 {member.id} 发送卡片确认消息失败: {str(e)}")
                else:
                    print(f"用户 {member.id} 不在线，无法发送卡片确认消息")

    async def _get_or_create_private_chat(self, user1_id: str, user2_id: str, db: Session) -> ChatRoom:
        """获取或创建两个用户之间的私聊聊天室"""
        # 查找用户是否已经有共同的私聊聊天室
        user1 = db.execute(select(User).filter(User.id == user1_id)).scalar_one_or_none()
        user2 = db.execute(select(User).filter(User.id == user2_id)).scalar_one_or_none()

        if not user2:
            # 如果用户2不存在，创建一个
            user2 = User(id=user2_id, username=f"用户{user2_id}")
            db.add(user2)
            db.commit()
            db.refresh(user2)

        # 查找两人共同的私聊房间
        stmt = select(ChatRoom).join(
            ChatRoom.members
        ).filter(
            User.id == user1_id,
            ChatRoom.is_group == False
        )

        shared_rooms = db.execute(stmt).scalars().all()

        for room in shared_rooms:
            members = [member.id for member in room.members]
            if user1_id in members and user2_id in members and len(members) == 2:
                return room

        # 如果没有找到，创建一个新的聊天室
        new_room = ChatRoom(
            is_group=False
        )
        new_room.members.append(user1)
        new_room.members.append(user2)

        db.add(new_room)
        db.commit()
        db.refresh(new_room)

        # 创建一个新的聊天室后，立即通知两位用户
        room_dict = new_room.to_dict()

        # 安全地发送通知
        try:
            # 为用户1发送通知
            if user1_id in self.websocket_connections:
                try:
                    await self.websocket_connections[user1_id].send_json({
                        "type": "new_chatroom",
                        "chatroom": room_dict
                    })
                    print(f"已通知用户 {user1_id} 新聊天室创建成功")
                except Exception as e:
                    print(f"通知用户 {user1_id} 新聊天室创建失败: {str(e)}")

            # 为用户2发送通知
            if user2_id in self.websocket_connections:
                try:
                    await self.websocket_connections[user2_id].send_json({
                        "type": "new_chatroom",
                        "chatroom": room_dict
                    })
                    print(f"已通知用户 {user2_id} 新聊天室创建成功")
                except Exception as e:
                    print(f"通知用户 {user2_id} 新聊天室创建失败: {str(e)}")
        except Exception as e:
            print(f"通知用户新聊天室创建失败: {str(e)}")

        return new_room

    async def broadcast(self, message: dict):
        # 循环变量给所有在线激活的链接发送消息-全局广播
        for connection in self.websocket_connections.values():
            await connection.send_json(message)

    async def disconnect(self, user_id: str):
        """断开用户连接并清理资源"""
        # 先获取WebSocket连接的引用，但暂时不从字典中移除
        websocket = self.websocket_connections.get(user_id)

        # 如果找不到连接，直接返回
        if not websocket:
            print(f"用户 {user_id} 的连接不存在或已被移除")
            return

        # 在关闭连接前，先从字典中移除，避免其他地方同时尝试关闭
        self.websocket_connections.pop(user_id, None)

        # 清理其他资源
        self.active_users.pop(user_id, None)
        self.last_heartbeat.pop(user_id, None)

        # 尝试关闭连接
        try:
            # 尝试安全地检查连接状态
            is_closed = False
            try:
                # 不同版本的FastAPI/Starlette可能有不同的状态属性和名称
                if hasattr(websocket, 'client_state'):
                    # Starlette 0.14.0+
                    client_state = getattr(websocket, 'client_state')
                    if hasattr(client_state, 'name'):
                        is_closed = client_state.name in ('DISCONNECTED', 'CLOSED')
                    elif hasattr(client_state, 'value'):
                        is_closed = client_state.value in (1, 3)  # 1=DISCONNECTED, 3=CLOSED
                elif hasattr(websocket, 'state'):
                    # 较旧版本
                    state = getattr(websocket, 'state')
                    if hasattr(state, 'name'):
                        is_closed = state.name in ('DISCONNECTED', 'CLOSED')
                    elif hasattr(state, 'value'):
                        is_closed = state.value in (1, 3)
            except Exception as state_error:
                print(f"检查WebSocket状态错误: {str(state_error)}")
                # 如果无法确定状态，假设连接未关闭
                is_closed = False

            # 只关闭未关闭的连接
            if not is_closed:
                await websocket.close()
                print(f"用户 {user_id} 的连接已关闭")
            else:
                print(f"用户 {user_id} 的连接已经处于关闭状态")
        except Exception as e:
            print(f"关闭WebSocket连接错误: {str(e)}")

        print(f"用户 {user_id} 的连接资源已清理完毕")

    # 为了向后兼容，保留close方法，但内部调用disconnect
    async def close(self, websocket: WebSocket, user_id: str):
        """断开指定WebSocket连接并清理资源（已废弃，请使用disconnect）"""
        print(f"警告: close方法已废弃，请使用disconnect方法 (user_id: {user_id})")
        # 确保连接在字典中
        if user_id in self.websocket_connections:
            # 如果当前连接与传入的不同，先更新字典
            if self.websocket_connections[user_id] != websocket:
                print(f"警告: 更新用户 {user_id} 的WebSocket连接")
                self.websocket_connections[user_id] = websocket
        else:
            # 如果用户ID不在字典中，但提供了WebSocket，添加到字典
            print(f"警告: 用户 {user_id} 不在连接字典中，添加连接")
            self.websocket_connections[user_id] = websocket

        # 调用disconnect方法断开连接
        await self.disconnect(user_id)

    def get_connection_stats(self):
        """获取当前连接状态的统计信息"""
        active_connections = len(self.websocket_connections)
        active_users = len(self.active_users)
        heartbeat_users = len(self.last_heartbeat)

        # 检查心跳状态
        now = time.time()
        heartbeat_stats = {
            "total": heartbeat_users,
            "healthy": 0,
            "warning": 0,
            "critical": 0
        }

        for user_id, last_time in self.last_heartbeat.items():
            time_since_last_heartbeat = int(now - last_time)
            if time_since_last_heartbeat <= self.heartbeat_timeout * 0.5:
                # 健康状态
                heartbeat_stats["healthy"] += 1
            elif time_since_last_heartbeat <= self.heartbeat_timeout * 0.8:
                # 警告状态
                heartbeat_stats["warning"] += 1
            else:
                # 临界状态
                heartbeat_stats["critical"] += 1

        return {
            "active_connections": active_connections,
            "active_users": active_users,
            "heartbeat_users": heartbeat_users,
            "heartbeat_stats": heartbeat_stats,
            "heartbeat_timeout": self.heartbeat_timeout,
            "running": self.running,
            "heartbeat_task_active": self.heartbeat_task is not None and not self.heartbeat_task.done()
        }
