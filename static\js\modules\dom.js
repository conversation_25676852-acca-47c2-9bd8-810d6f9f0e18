/**
 * DOM元素引用和基本操作
 */

// DOM 元素引用
export const elements = {
    // 认证相关
    loginContainer: document.getElementById('login-container'),
    loginUsername: document.getElementById('login-username'),
    loginBtn: document.getElementById('login-btn'),

    // 用户信息
    userAvatar: document.getElementById('user-avatar'),
    username: document.getElementById('username'),

    // 聊天界面
    chatList: document.getElementById('chat-list'),
    currentChatName: document.getElementById('current-chat-name'),
    chatMessages: document.getElementById('chat-messages'),
    messageInput: document.getElementById('message-input'),
    sendBtn: document.getElementById('send-btn'),
    loadMoreBtn: document.getElementById('load-more-btn'),

    // 按钮
    newChatBtn: document.getElementById('new-chat-btn'),
    newGroupBtn: document.getElementById('new-group-btn'),
    addUserBtn: document.getElementById('add-user-btn'),

    // 消息工具栏
    emojiBtn: document.getElementById('emoji-btn'),
    imageBtn: document.getElementById('image-btn'),
    quickReplyBtn: document.getElementById('quick-reply-btn'),
    imageUpload: document.getElementById('image-upload'),
    emojiPicker: document.getElementById('emoji-picker'),
    quickReplyPanel: document.getElementById('quick-reply-panel'),

    // 模态框
    newChatModal: document.getElementById('new-chat-modal'),
    newGroupModal: document.getElementById('new-group-modal'),
    userSearch: document.getElementById('user-search'),
    searchBtn: document.getElementById('search-btn'),
    userSearchResults: document.getElementById('user-search-results'),
    closeModals: document.querySelectorAll('.close-modal'),

    // 群组
    groupName: document.getElementById('group-name'),
    groupUserSearch: document.getElementById('group-user-search'),
    groupSearchBtn: document.getElementById('group-search-btn'),
    groupUserSearchResults: document.getElementById('group-user-search-results'),
    selectedMembersEl: document.getElementById('selected-members'),
    createGroupBtn: document.getElementById('create-group-btn'),

    // 群组成员
    groupMembersPanel: document.getElementById('group-members-panel'),
    membersList: document.getElementById('members-list'),
    panelAddMemberBtn: document.getElementById('panel-add-member-btn')
};

/**
 * 更新UI状态
 * @param {boolean} connected - WebSocket是否已连接
 * @param {object} currentChatroom - 当前选中的聊天室
 */
export function updateUIState(connected = true, currentChatroom = null) {
    if (connected) {
        // 更新消息输入区状态
        elements.messageInput.disabled = !currentChatroom;
        elements.sendBtn.disabled = !currentChatroom;

        // 更新消息工具栏状态
        if (elements.emojiBtn) elements.emojiBtn.disabled = !currentChatroom;
        if (elements.imageBtn) elements.imageBtn.disabled = !currentChatroom;
        if (elements.quickReplyBtn) elements.quickReplyBtn.disabled = !currentChatroom;

        // 如果没有选中聊天室，隐藏选择器
        if (!currentChatroom) {
            if (elements.emojiPicker) elements.emojiPicker.style.display = 'none';
            if (elements.quickReplyPanel) elements.quickReplyPanel.style.display = 'none';
        }
    } else {
        // 断开连接时禁用所有输入控件
        elements.messageInput.disabled = true;
        elements.sendBtn.disabled = true;

        if (elements.emojiBtn) elements.emojiBtn.disabled = true;
        if (elements.imageBtn) elements.imageBtn.disabled = true;
        if (elements.quickReplyBtn) elements.quickReplyBtn.disabled = true;

        alert('连接已断开，请刷新页面重新连接');
    }

    // 如果消息工具模块已加载，调用其启用/禁用方法
    if (typeof window.enableMessageTools === 'function') {
        window.enableMessageTools(connected && currentChatroom);
    }
}

/**
 * 隐藏登录表单
 */
export function hideLoginForm() {
    elements.loginContainer.style.display = 'none';
}

/**
 * 显示/隐藏加载更多按钮
 * @param {boolean} show - 是否显示按钮
 */
export function toggleLoadMoreButton(show) {
    elements.loadMoreBtn.style.display = show ? 'block' : 'none';
}

/**
 * 添加加载指示器
 * @returns {HTMLElement} 创建的加载指示器元素
 */
export function addLoadingIndicator() {
    const loadingIndicator = document.createElement('div');
    loadingIndicator.className = 'message-system';
    loadingIndicator.textContent = '加载更多消息...';
    loadingIndicator.id = 'loading-indicator';
    elements.chatMessages.insertBefore(loadingIndicator, elements.chatMessages.firstChild);
    return loadingIndicator;
}

/**
 * 移除加载指示器
 */
export function removeLoadingIndicator() {
    const indicator = document.getElementById('loading-indicator');
    if (indicator) indicator.remove();
}

/**
 * 清空聊天消息区域
 */
export function clearMessages() {
    elements.chatMessages.innerHTML = '';
}

/**
 * 显示空状态消息
 * @param {string} message - 要显示的消息
 */
export function showEmptyState(message) {
    clearMessages();
    const emptyMsg = document.createElement('div');
    emptyMsg.className = 'empty-state';
    emptyMsg.textContent = message;
    elements.chatMessages.appendChild(emptyMsg);
}

/**
 * 设置当前聊天名称
 * @param {string} name - 聊天名称
 */
export function setCurrentChatName(name) {
    elements.currentChatName.textContent = name;
}

/**
 * 滚动到最新消息
 */
export function scrollToLatestMessage() {
    elements.chatMessages.scrollTop = elements.chatMessages.scrollHeight;
}